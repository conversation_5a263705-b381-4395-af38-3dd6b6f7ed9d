# 组织类型功能测试指南

## 修改总结

我们已经成功实现了在 `sys_dept` 表中添加 `org_type` 字段来区分单位和部门的功能。以下是完成的修改：

### 1. 数据库层面
- ✅ 添加了 `org_type` 字段到 `sys_dept` 表
- ✅ 创建了组织类型字典数据
- ✅ 为现有数据设置默认值

### 2. 实体类修改
- ✅ 在 `SysDept` 实体类中添加了 `orgType` 属性
- ✅ 添加了相应的 getter/setter 方法
- ✅ 更新了 `toString()` 方法

### 3. 数据访问层修改
- ✅ 更新了 `SysDeptMapper.xml` 中的 resultMap 映射
- ✅ 更新了查询语句中的字段列表
- ✅ 更新了插入和更新语句
- ✅ 在查询条件中添加了对 orgType 的支持

### 4. 业务逻辑层修改
- ✅ 在 `ISysDeptService` 中添加了按组织类型查询的方法
- ✅ 在 `SysDeptServiceImpl` 中实现了这些方法
- ✅ 添加了单位和部门的专门查询方法

### 5. 控制器层修改
- ✅ 在 `SysDeptController` 中添加了按组织类型查询的接口
- ✅ 新增了 `/units`、`/departments`、`/unitTree`、`/departmentTree` 等接口

### 6. 常量定义
- ✅ 在 `UserConstants` 中添加了组织类型常量

## 新增的API接口

### 1. 获取单位列表
```
GET /system/dept/units
```

### 2. 获取部门列表（仅部门类型）
```
GET /system/dept/departments
```

### 3. 获取单位树列表
```
GET /system/dept/unitTree
```

### 4. 获取部门树列表（仅部门类型）
```
GET /system/dept/departmentTree
```

## 测试步骤

### 1. 执行数据库脚本
```sql
-- 执行 sql/add_org_type_field.sql 中的脚本
```

### 2. 重启应用
重启 tocc-backend 应用以加载新的代码

### 3. 测试API接口
使用 Postman 或其他工具测试新增的API接口

### 4. 验证数据
- 检查现有数据是否正确设置了 `org_type` 字段
- 测试新增组织时是否可以选择组织类型
- 测试按组织类型过滤查询是否正常工作

## 常量定义

```java
// 组织类型常量
UserConstants.ORG_TYPE_DEPT = "0"  // 部门
UserConstants.ORG_TYPE_UNIT = "1"  // 单位
```

## 字典配置

系统会自动创建以下字典数据：
- 字典类型：`org_type`
- 字典数据：
  - 部门：值为 "0"
  - 单位：值为 "1"

## 兼容性说明

- 现有的 `/system/dept/list` 接口保持不变，会返回所有类型的组织
- 现有的 `/system/dept/treeselect` 接口保持不变
- 新增的接口都是可选的，不会影响现有功能
- 现有数据会被默认设置为单位类型（org_type = '1'）

## 下一步工作

1. **前端界面修改**：在部门管理页面添加组织类型选择和过滤功能
2. **权限配置**：根据需要配置单位和部门的不同权限
3. **业务逻辑优化**：根据实际业务需求调整组织类型的使用逻辑
4. **数据迁移**：根据实际情况调整现有数据的组织类型
