package com.tocc.risk.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 检查任务对象 risk_inspect_task
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@ApiModel(value = "检查任务对象")
public class InspectTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /** 任务下发表ID */
    @Excel(name = "任务下发表ID")
    @ApiModelProperty(value = "任务下发表ID")
    private Long issuedId;

    /** 填报人ID */
    @Excel(name = "填报人ID")
    @ApiModelProperty(value = "填报人ID")
    private Long informantId;

    /** 填报人姓名 */
    @Excel(name = "填报人姓名")
    @ApiModelProperty(value = "填报人姓名")
    private String informant;

    /** 所属领域 */
    @Excel(name = "所属领域")
    @ApiModelProperty(value = "所属领域")
    private String area;

    /** 填报内容json格式 */
    @Excel(name = "填报内容json格式")
    @ApiModelProperty(value = "填报内容json格式")
    private String contents;

    /** 填报状态（0-待填报，1-已填报，2-已驳回） */
    @Excel(name = "填报状态", readConverterExp = "0=-待处理，1-已填报，2-已驳回")
    @ApiModelProperty(value = "填报状态")
    private Integer status;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "填报状态")
    private Integer delFlag;

    /** 审核人ID */
    @ApiModelProperty(value = "审核人ID")
    private Long approveId;

    /** 项目ID */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "单位填报人ID")
    private String fillIds;

    @ApiModelProperty(value = "项目填报人ID")
    private String builderIds;

    public void setProjectId(String projectId)
    {
        this.projectId = projectId;
    }

    public String getProjectId()
    {
        return projectId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setApproveId(Long approveId)
    {
        this.approveId = approveId;
    }

    public Long getApproveId()
    {
        return approveId;
    }

    public void setIssuedId(Long issuedId) 
    {
        this.issuedId = issuedId;
    }

    public Long getIssuedId() 
    {
        return issuedId;
    }

    public void setInformantId(Long informantId) 
    {
        this.informantId = informantId;
    }

    public Long getInformantId() 
    {
        return informantId;
    }

    public void setInformant(String informant)
    {
        this.informant = informant;
    }

    public String getInformant() 
    {
        return informant;
    }

    public void setArea(String area)
    {
        this.area = area;
    }

    public String getArea()
    {
        return area;
    }

    public void setFillIds(String fillIds)
    {
        this.fillIds = fillIds;
    }

    public String getFillIds()
    {
        return fillIds;
    }

    public void setBuilderIds(String builderIds)
    {
        this.builderIds = builderIds;
    }

    public String getBuilderIds()
    {
        return builderIds;
    }

    public void setContents(String contents) 
    {
        this.contents = contents;
    }

    public String getContents() 
    {
        return contents;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("approveId", getApproveId())
            .append("issuedId", getIssuedId())
            .append("informantId", getInformantId())
            .append("Informant", getInformant())
            .append("area", getArea())
            .append("projectId", getProjectId())
            .append("contents", getContents())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
