package com.tocc.risk.service.impl;

import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.utils.DateUtils;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.mapper.InspectIssuedMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.FillFieldsMapper;
import com.tocc.risk.domain.FillFields;
import com.tocc.risk.service.IFillFieldsService;

/**
 * 填报项字段Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class FillFieldsServiceImpl extends ServiceImpl<FillFieldsMapper, FillFields> implements IFillFieldsService
{
    @Autowired
    private FillFieldsMapper fillFieldsMapper;
    @Autowired
    private InspectIssuedMapper issuedMapper;

    /**
     * 查询填报项字段
     * 
     * @param id 填报项字段主键
     * @return 填报项字段
     */
    @Override
    public FillFields selectFillFieldsById(Long id)
    {
        return fillFieldsMapper.selectFillFieldsById(id);
    }

    /**
     * 查询填报项字段列表
     * 
     * @param fillFields 填报项字段
     * @return 填报项字段
     */
    @Override
    public List<FillFields> selectFillFieldsList(FillFields fillFields)
    {
        return fillFieldsMapper.selectFillFieldsList(fillFields);
    }

    @Override
    public List<FillFields> getFieldsByIssuedId(Long id) {
        InspectIssued issued = issuedMapper.selectInspectIssuedById(id);
        String[] fields = issued.getFields().split(",");
        fillFieldsMapper.getListByIds(fields);
        return Collections.emptyList();
    }

    /**
     * 新增填报项字段
     * 
     * @param fillFields 填报项字段
     * @return 结果
     */
    @Override
    public int insertFillFields(FillFields fillFields)
    {
        fillFields.setCreateTime(DateUtils.getNowDate());
        fillFields.setDelFlag(0);
        return fillFieldsMapper.insertFillFields(fillFields);
    }

    /**
     * 修改填报项字段
     * 
     * @param fillFields 填报项字段
     * @return 结果
     */
    @Override
    public int updateFillFields(FillFields fillFields)
    {
        fillFields.setUpdateTime(DateUtils.getNowDate());
        return fillFieldsMapper.updateFillFields(fillFields);
    }

    /**
     * 批量删除填报项字段
     * 
     * @param ids 需要删除的填报项字段主键
     * @return 结果
     */
    @Override
    public int deleteFillFieldsByIds(Long[] ids)
    {
        return fillFieldsMapper.deleteFillFieldsByIds(ids);
    }

    /**
     * 删除填报项字段信息
     * 
     * @param id 填报项字段主键
     * @return 结果
     */
    @Override
    public int deleteFillFieldsById(Long id)
    {
        return fillFieldsMapper.deleteFillFieldsById(id);
    }
}
