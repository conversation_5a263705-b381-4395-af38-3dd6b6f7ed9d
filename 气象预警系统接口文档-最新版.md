# 气象预警系统接口文档（最新版）

## 概述

气象预警系统提供预警信息的创建、查询、通知发送、通知确认等功能。系统采用两步式流程：先创建预警信息（模拟接收气象部门预警），再针对预警发送通知给相关单位和人员。

### 基础信息

- **基础URL**: `/weather`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 业务流程

1. **创建预警**：模拟接收气象部门发布的预警信息，创建预警记录并生成预警发布告警
2. **发送通知**：针对已创建的预警，选择通知对象并发送通知，为每个单位生成通知确认告警

### 告警机制

- **预警发布告警**：创建预警时生成，归属于广西壮族自治区交通运输厅
  - 告警类型：3（气象告警）
  - 告警子类型：7（预警发布）
  - 业务类型：warning_publish
- **通知确认告警**：发送通知时生成，按单位分组，归属于各通知对象所在单位
  - 告警类型：3（气象告警）
  - 告警子类型：8（通知确认）
  - 业务类型：notification_confirm

### 预警类型字典

| 值 | 名称 | 描述 |
|---|---|---|
| 1 | 暴雨预警 | 暴雨天气预警 |
| 2 | 台风预警 | 台风天气预警 |
| 3 | 雷电预警 | 雷电天气预警 |
| 4 | 大风预警 | 大风天气预警 |
| 5 | 冰雹预警 | 冰雹天气预警 |
| 6 | 高温预警 | 高温天气预警 |
| 7 | 寒潮预警 | 寒潮天气预警 |
| 8 | 大雾预警 | 大雾天气预警 |
| 9 | 道路结冰预警 | 道路结冰预警 |
| 10 | 霜冻预警 | 霜冻天气预警 |

### 预警等级字典

| 值 | 名称 | 颜色 | 描述 |
|---|---|---|---|
| 5 | 蓝色预警 | #0066FF | 一般级别预警 |
| 6 | 黄色预警 | #FFCC00 | 较重级别预警 |
| 7 | 橙色预警 | #FF8C00 | 严重级别预警 |
| 8 | 红色预警 | #FF0000 | 特别严重级别预警 |

### 预警状态字典

| 值 | 名称 | 描述 |
|---|---|---|
| 0 | 有效 | 预警有效 |
| 1 | 失效 | 预警失效 |
| 2 | 取消 | 预警取消 |

### 通知状态字典

| 值 | 名称 | 描述 |
|---|---|---|
| 0 | 未通知 | 尚未发送通知 |
| 1 | 已通知 | 已发送通知 |

### 确认状态字典

| 值 | 名称 | 描述 |
|---|---|---|
| 0 | 未确认 | 尚未确认 |
| 1 | 已确认 | 已经确认 |

---

## 1. 预警管理接口

### 1.1 创建预警信息

**接口地址**: `POST /weather/warning`

**接口描述**: 创建气象预警信息（模拟接收气象部门预警），同时生成预警发布告警

**权限要求**: `weather:warning:add`

**业务说明**:
- 创建预警记录，状态为"有效"，通知状态为"未通知"
- 自动生成一条预警发布告警，归属于广西壮族自治区交通运输厅
- 告警标题格式：`"发布[预警类型][预警等级]"`（如：发布暴雨预警红色预警）

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningType | String | 是 | 预警类型 | "1" |
| warningLevel | String | 是 | 预警等级 | "8" |
| warningContent | String | 是 | 预警内容 | "预计未来3小时内..." |
| preventionGuide | String | 否 | 防御指南 | "政府及相关部门..." |
| affectedRoads | String | 否 | 受影响道路 | "G80广昆高速..." |
| issueTime | String | 是 | 发布时间 | "2024-12-19 10:00:00" |
| expireTime | String | 否 | 失效时间 | "2024-12-19 18:00:00" |
| affectedAreas | Array | 是 | 影响区域列表 | 见下方示例 |

**影响区域对象结构**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| regionId | String | 是 | 行政区划ID | "450100" |
| regionName | String | 是 | 行政区划名称 | "南宁市" |

**请求示例**:

```json
{
  "warningType": "1",
  "warningLevel": "8",
  "warningContent": "预计未来3小时内，南宁市区及各县区将出现100毫米以上强降雨，局部地区可能超过150毫米，并伴有雷电、短时大风等强对流天气。请注意防范山洪、地质灾害、城市内涝等次生灾害。",
  "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作；切断有危险的室外电源，暂停户外作业；处于危险地带的单位应当停课、停业，采取专门措施保护已到校学生、幼儿和其他上班人员的安全。",
  "affectedRoads": "G80广昆高速玉林段；S21玉铁高速全线；G324国道玉林至北流段",
  "issueTime": "2024-12-19 10:00:00",
  "expireTime": "2024-12-19 18:00:00",
  "affectedAreas": [
    {
      "regionId": "450100",
      "regionName": "南宁市"
    },
    {
      "regionId": "450103",
      "regionName": "青秀区"
    },
    {
      "regionId": "450105",
      "regionName": "江南区"
    }
  ]
}
```

**返回示例**:

```json
{
  "code": 200,
  "msg": "预警创建成功，ID：uuid-123",
  "data": null
}
```

### 1.2 发送预警通知

**接口地址**: `POST /weather/warning/{warningId}/notify`

**接口描述**: 针对已创建的预警发送通知给指定人员，同时生成通知确认告警

**权限要求**: `weather:warning:add`

**业务说明**:
- 更新预警的通知状态为"已通知"
- 创建通知记录给指定的联系人
- 按单位分组生成通知确认告警，归属于各通知对象所在单位
- 告警标题格式：`"[预警类型][预警等级]通知确认"`（如：暴雨预警红色预警通知确认）

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "uuid-123" |
| notifyTargets | Array | 是 | 通知对象列表 | 见下方示例 |

**通知对象结构**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| contactUserId | Long | 是 | 联系人用户ID | 1 |
| contactUserName | String | 是 | 联系人姓名 | "张三" |
| contactUnitId | String | 是 | 联系人单位ID | "101" |
| contactUnitName | String | 是 | 联系人单位名称 | "南宁市应急管理局" |
| contactDeptName | String | 否 | 联系人部门名称 | "综合科" |
| contactPostName | String | 否 | 联系人岗位名称 | "科长" |
| contactPhone | String | 否 | 联系人电话 | "13800138000" |
| contactEmail | String | 否 | 联系人邮箱 | "<EMAIL>" |

**请求示例**:

```json
{
  "notifyTargets": [
    {
      "contactUserId": 1,
      "contactUserName": "张三",
      "contactUnitId": "101",
      "contactUnitName": "南宁市应急管理局",
      "contactDeptName": "综合科",
      "contactPostName": "科长",
      "contactPhone": "13800138000",
      "contactEmail": "<EMAIL>"
    },
    {
      "contactUserId": 2,
      "contactUserName": "李四",
      "contactUnitId": "101",
      "contactUnitName": "南宁市应急管理局",
      "contactDeptName": "安全监管科",
      "contactPostName": "副科长",
      "contactPhone": "13800138001",
      "contactEmail": "<EMAIL>"
    },
    {
      "contactUserId": 3,
      "contactUserName": "王五",
      "contactUnitId": "102",
      "contactUnitName": "南宁市交通运输局",
      "contactDeptName": "运输管理科",
      "contactPostName": "科员",
      "contactPhone": "13800138002",
      "contactEmail": "<EMAIL>"
    }
  ]
}
```

**返回示例**:

```json
{
  "code": 200,
  "msg": "通知发送成功，共3条",
  "data": null
}
```

**业务结果**:
- 创建3条通知记录
- 生成2条通知确认告警（按单位分组：应急管理局1条，交通运输局1条）

### 1.3 查询预警列表

**接口地址**: `GET /weather/warning/list`

**接口描述**: 分页查询气象预警信息列表，支持多种条件筛选

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningType | String | 否 | 预警类型 | "1" |
| warningLevel | String | 否 | 预警等级 | "8" |
| status | String | 否 | 状态 | "0" |
| isNotified | String | 否 | 是否已通知 | "1" |
| issueTimeStart | String | 否 | 发布时间开始 | "2024-01-01 00:00:00" |
| issueTimeEnd | String | 否 | 发布时间结束 | "2024-12-31 23:59:59" |
| regionId | String | 否 | 影响区域ID | "450100" |
| createBy | String | 否 | 创建人 | "admin" |
| contentKeyword | String | 否 | 内容关键字 | "暴雨" |
| pageNum | Integer | 否 | 页码 | 1 |
| pageSize | Integer | 否 | 每页数量 | 10 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "warningId": "uuid-123",
      "warningType": "1",
      "warningLevel": "8",
      "warningContent": "预计未来3小时内，南宁市区及各县区将出现100毫米以上强降雨...",
      "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作...",
      "affectedRoads": "G80广昆高速玉林段、S21玉铁高速全线...",
      "issueTime": "2024-12-19 10:00:00",
      "expireTime": "2024-12-19 18:00:00",
      "status": "0",
      "isNotified": "1",
      "createBy": "admin",
      "createTime": "2024-12-19 09:55:00",
      "affectedAreasDesc": "南宁市、青秀区、江南区",
      "totalNotifications": 3,
      "confirmedNotifications": 2,
      "unconfirmedNotifications": 1,
      "timeoutNotifications": 0
    }
  ],
  "total": 1
}
```

### 1.4 获取预警详情

**接口地址**: `GET /weather/warning/{warningId}`

**接口描述**: 根据预警ID获取详细信息，包括影响区域和通知统计

**权限要求**: `weather:warning:query`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "uuid-123" |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "warningId": "uuid-123",
    "warningType": "1",
    "warningTypeLabel": "暴雨预警",
    "warningLevel": "8",
    "warningLevelLabel": "红色预警",
    "warningContent": "预计未来3小时内，南宁市区及各县区将出现100毫米以上强降雨...",
    "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作...",
    "affectedRoads": "G80广昆高速玉林段、S21玉铁高速全线...",
    "issueTime": "2024-12-19 10:00:00",
    "expireTime": "2024-12-19 18:00:00",
    "status": "0",
    "statusLabel": "有效",
    "isNotified": "1",
    "isNotifiedLabel": "已通知",
    "createTime": "2024-12-19 09:55:00",
    "createBy": "admin",
    "affectedAreas": [
      {
        "warningId": "uuid-123",
        "regionId": "450100",
        "regionName": "南宁市",
        "regionFullPath": "广西壮族自治区/南宁市",
        "createTime": "2024-12-19 09:55:00"
      }
    ],
    "affectedAreasDesc": "南宁市、青秀区、江南区",
    "totalNotifications": 3,
    "confirmedNotifications": 2,
    "unconfirmedNotifications": 1,
    "timeoutNotifications": 0
  }
}
```

### 1.5 更新预警状态

**接口地址**: `PUT /weather/warning/{warningId}/status`

**接口描述**: 更新预警状态（有效/失效/取消）

**权限要求**: `weather:warning:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "uuid-123" |
| status | String | 是 | 新状态（请求体） | "1" |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 1.6 取消预警

**接口地址**: `PUT /weather/warning/{warningId}/cancel`

**接口描述**: 取消预警并记录取消原因

**权限要求**: `weather:warning:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "uuid-123" |
| reason | String | 是 | 取消原因（请求体） | "天气好转，预警解除" |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 1.7 查询有效预警

**接口地址**: `GET /weather/warning/active`

**接口描述**: 查询当前有效的预警列表

**权限要求**: `weather:warning:list`

**请求参数**: 无

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "warningType": "1",
      "warningLevel": "8",
      "warningContent": "预计未来3小时内...",
      "issueTime": "2024-12-19 10:00:00",
      "expireTime": "2024-12-19 18:00:00",
      "status": "0",
      "isNotified": "1",
      "affectedAreasDesc": "南宁市、青秀区"
    }
  ]
}
```

### 1.8 测试接口

**接口地址**: `GET /weather/warning/test`

**接口描述**: 测试接口，验证气象预警模块是否正常运行

**权限要求**: 无

**请求参数**: 无

**返回示例**:

```json
{
  "code": 200,
  "msg": "气象预警模块正常运行"
}
```

---

## 2. 通知管理接口

### 2.1 查询通知进展

**接口地址**: `GET /weather/notification/progress/{warningId}`

**接口描述**: 查询指定预警的通知进展情况，包括确认状态

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "uuid-123" |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "contactUserId": 1,
      "contactUnitName": "南宁市应急管理局",
      "contactDeptName": "综合科",
      "contactPostName": "科长",
      "contactUserName": "张三",
      "contactPhone": "13800138000",
      "notificationTime": "2024-12-19 10:05:00",
      "confirmStatus": "1",
      "confirmStatusLabel": "已确认",
      "confirmTime": "2024-12-19 10:10:00",
      "confirmUserName": "张三"
    },
    {
      "warningId": "uuid-123",
      "contactUserId": 2,
      "contactUnitName": "南宁市应急管理局",
      "contactDeptName": "安全监管科",
      "contactPostName": "副科长",
      "contactUserName": "李四",
      "contactPhone": "13800138001",
      "notificationTime": "2024-12-19 10:05:00",
      "confirmStatus": "0",
      "confirmStatusLabel": "未确认",
      "confirmTime": null,
      "confirmUserName": null
    }
  ]
}
```

### 2.2 确认预警通知

**接口地址**: `POST /weather/notification/confirm/{warningId}/{contactUserId}`

**接口描述**: 确认收到的预警通知

**权限要求**: `weather:notification:confirm`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "uuid-123" |
| contactUserId | Long | 是 | 联系人用户ID（路径参数） | 1 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "确认成功"
}
```

**错误返回示例**:

```json
{
  "code": 500,
  "msg": "确认失败，请检查通知是否存在或已确认"
}
```

### 2.3 催办未确认通知

**接口地址**: `POST /weather/notification/remind/{warningId}`

**接口描述**: 对未确认的通知进行催办，重新发送通知

**权限要求**: `weather:warning:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "uuid-123" |

**返回示例**:

```json
{
  "code": 200,
  "msg": "催办通知已发送，共2条"
}
```

### 2.4 查询我的预警通知

**接口地址**: `GET /weather/notification/my`

**接口描述**: 查询当前登录用户收到的预警通知列表

**权限要求**: `weather:notification:list`

**请求参数**: 无（从当前登录用户获取）

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "contactUserId": 1,
      "contactUnitName": "南宁市应急管理局",
      "contactDeptName": "综合科",
      "contactPostName": "科长",
      "contactUserName": "张三",
      "contactPhone": "13800138000",
      "contactEmail": "<EMAIL>",
      "notificationTime": "2024-12-19 10:05:00",
      "confirmStatus": "0",
      "confirmTime": null,
      "createTime": "2024-12-19 10:05:00",
      "warning": {
        "warningId": "uuid-123",
        "warningType": "1",
        "warningLevel": "8",
        "warningContent": "预计未来3小时内...",
        "issueTime": "2024-12-19 10:00:00",
        "status": "0"
      }
    }
  ]
}
```

---

## 3. 错误码说明

### 3.1 通用错误码

| 错误码 | HTTP状态码 | 描述 |
|---|---|---|
| 200 | 200 | 操作成功 |
| 401 | 401 | 未授权，请先登录 |
| 403 | 403 | 权限不足 |
| 404 | 404 | 资源不存在 |
| 500 | 500 | 服务器内部错误 |

### 3.2 业务错误码

| 错误码 | 描述 |
|---|---|
| WARNING_NOT_FOUND | 预警不存在 |
| WARNING_EXPIRED | 预警已过期 |
| INVALID_WARNING_STATUS | 无效的预警状态 |
| NOTIFICATION_ALREADY_CONFIRMED | 通知已确认 |
| NOTIFICATION_SEND_FAILED | 通知发送失败 |
| PARAMETER_VALIDATION_ERROR | 参数验证错误 |
| BUSINESS_RULE_VIOLATION | 业务规则违反 |

---

## 4. 告警机制说明

### 4.1 告警类型

系统中的气象预警告警使用以下配置：

#### 预警发布告警
- **告警类型**: 3（气象告警）
- **告警子类型**: 7（预警发布）
- **业务类型**: warning_publish

#### 通知确认告警
- **告警类型**: 3（气象告警）
- **告警子类型**: 8（通知确认）
- **业务类型**: notification_confirm

### 4.2 预警发布告警

**触发时机**: 创建预警时自动生成

**告警属性**:
- **归属单位**: 广西壮族自治区交通运输厅（单位ID：100）
- **告警标题**: `"发布[预警类型][预警等级]"`
- **告警内容**: 包含发布时间、影响区域、预警详情等
- **数量**: 每个预警生成1条告警

**示例**:
```
标题：发布暴雨预警红色预警
内容：2024年12月19日10时00分，南宁市、青秀区、江南区等3个区域发布暴雨预警红色预警，失效时间：2024年12月19日18时00分

预警内容：
预计未来3小时内，南宁市区及各县区将出现100毫米以上强降雨...

防御指南：
政府及相关部门按照职责做好防暴雨应急工作...
```

### 4.3 通知确认告警

**触发时机**: 发送通知时按单位分组生成

**告警属性**:
- **归属单位**: 通知对象所在单位
- **告警标题**: `"[预警类型][预警等级]通知确认"`
- **告警内容**: 包含需要确认的人员列表
- **数量**: 按单位分组，每个单位生成1条告警

**示例**:
```
标题：暴雨预警红色预警通知确认
内容：2024年12月19日10时00分发布的暴雨预警红色预警需要以下人员确认：
- 张三（科长），13800138000
- 李四（副科长），13800138001

请督促相关人员及时确认预警信息。
```

### 4.4 告警查询

#### 查询预警发布告警
```sql
SELECT * FROM alarm_info
WHERE alarm_type = '3'
  AND alarm_subtype = '7'
  AND subtype = 'warning_publish'
  AND source_id = 'warning_id';
```

#### 查询通知确认告警
```sql
SELECT * FROM alarm_info
WHERE alarm_type = '3'
  AND alarm_subtype = '8'
  AND subtype = 'notification_confirm'
  AND source_id = 'warning_id';
```

#### 查询指定单位的通知确认告警
```sql
SELECT * FROM alarm_info
WHERE alarm_type = '3'
  AND alarm_subtype = '8'
  AND subtype = 'notification_confirm'
  AND org_id = 'unit_id';
```

#### 根据告警ID反查通知确认进度
```sql
-- 1. 从告警表获取预警ID
SELECT source_id as warning_id FROM alarm_info WHERE alarm_id = 'alarm_id';

-- 2. 查询该预警的通知确认进度
SELECT * FROM weather_warning_notification WHERE warning_id = 'warning_id';
```

---

## 5. 使用示例

### 5.1 完整业务流程示例

#### 步骤1：创建预警

```bash
curl -X POST "http://localhost:8380/weather/warning" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "warningType": "1",
    "warningLevel": "8",
    "warningContent": "预计未来3小时内，南宁市区将出现100毫米以上强降雨",
    "issueTime": "2024-12-19 10:00:00",
    "expireTime": "2024-12-19 18:00:00",
    "affectedAreas": [
      {"regionId": "450100", "regionName": "南宁市"},
      {"regionId": "450103", "regionName": "青秀区"}
    ]
  }'
```

**结果**:
- 创建预警记录（状态：有效，通知状态：未通知）
- 生成1条预警发布告警（归属：广西交通运输厅）

#### 步骤2：发送通知

```bash
curl -X POST "http://localhost:8380/weather/warning/uuid-123/notify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "notifyTargets": [
      {
        "contactUserId": 1,
        "contactUserName": "张三",
        "contactUnitId": "101",
        "contactUnitName": "南宁市应急管理局",
        "contactPhone": "13800138000"
      },
      {
        "contactUserId": 2,
        "contactUserName": "李四",
        "contactUnitId": "101",
        "contactUnitName": "南宁市应急管理局",
        "contactPhone": "13800138001"
      }
    ]
  }'
```

**结果**:
- 更新预警通知状态为"已通知"
- 创建2条通知记录
- 生成1条通知确认告警（归属：南宁市应急管理局）

#### 步骤3：确认通知

```bash
curl -X POST "http://localhost:8380/weather/notification/confirm/uuid-123/1" \
  -H "Authorization: Bearer your-jwt-token"
```

**结果**: 更新通知确认状态

### 5.2 查询接口示例

```bash
# 查询预警列表
curl -X GET "http://localhost:8380/weather/warning/list?warningType=1&isNotified=1" \
  -H "Authorization: Bearer your-jwt-token"

# 查询通知进展
curl -X GET "http://localhost:8380/weather/notification/progress/uuid-123" \
  -H "Authorization: Bearer your-jwt-token"

# 查询我的通知
curl -X GET "http://localhost:8380/weather/notification/my" \
  -H "Authorization: Bearer your-jwt-token"
```

---

## 6. 注意事项

1. **权限控制**: 所有接口都需要相应的权限才能访问
2. **数据验证**: 创建预警和发送通知时会验证必填字段和数据格式
3. **告警生成**: 系统会自动生成相应的告警记录，无需手动创建
4. **单位分组**: 通知确认告警按单位ID分组生成，避免告警过多
5. **业务流程**: 必须先创建预警，再发送通知，不能跳过步骤
6. **日志记录**: 所有操作都会记录详细的日志信息

---

## 7. 数据库字段说明

### 7.1 weather_warning表新增字段

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| is_notified | CHAR(1) | 是否已通知（0-未通知 1-已通知） | '0' |

### 7.2 告警相关字段

| 字段名 | 值 | 描述 |
|---|---|---|
| alarm_type | "3" | 气象告警 |
| alarm_subtype | "7" | 气象告警子类型 |
| org_id | "100" | 预警发布告警归属交通运输厅 |
| org_id | 通知对象单位ID | 通知确认告警归属对应单位 |

---

## 8. 联系方式

如有问题请联系开发团队。
