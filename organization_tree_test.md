# 组织架构树接口测试指南

## 🔄 更新内容

我们已经成功更新了 `/system/organization/tree` 接口，现在支持完整的**单位-部门-岗位-人员**四级层级结构。

## 🌳 层级结构说明

### 新的层级结构
```
单位（unit）
├── 部门A（dept）
│   ├── 岗位1（post）
│   │   ├── 用户1（user）
│   │   └── 用户2（user）
│   ├── 岗位2（post）
│   └── 直属用户（user，无岗位）
├── 部门B（dept）
└── 直属岗位（post，直接属于单位）
    └── 用户3（user）
```

### 节点类型和ID前缀
- **单位**：`type: "unit"`, `id: "unit_100"`
- **部门**：`type: "dept"`, `id: "dept_101"`
- **岗位**：`type: "post"`, `id: "post_1"`
- **用户**：`type: "user"`, `id: "user_1"`

## 📋 接口详情

### 主接口：获取完整组织架构树
**接口地址**：`GET /system/organization/tree`

**请求参数**：
```json
{
  "deptId": "根节点ID（可选）"
}
```

**返回数据结构**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "unit_100",
      "name": "应急管理局",
      "type": "unit",
      "deptId": 100,
      "parentId": null,
      "phone": "13800138000",
      "email": "<EMAIL>",
      "leader": "张三",
      "status": "0",
      "orderNum": 0,
      "children": [
        {
          "id": "dept_101",
          "name": "综合科",
          "type": "dept",
          "deptId": 101,
          "parentId": "unit_100",
          "phone": "13800138001",
          "email": "<EMAIL>",
          "leader": "李四",
          "status": "0",
          "orderNum": 1,
          "children": [
            {
              "id": "post_1",
              "name": "科长",
              "type": "post",
              "postId": 1,
              "parentId": "dept_101",
              "postCode": "001",
              "status": "0",
              "orderNum": 1,
              "children": [
                {
                  "id": "user_1",
                  "name": "王五",
                  "type": "user",
                  "userId": 1,
                  "parentId": "post_1",
                  "phone": "13800138002",
                  "email": "<EMAIL>",
                  "userName": "wangwu",
                  "nickName": "王五",
                  "status": "0",
                  "children": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 🔍 测试步骤

### 1. 基本功能测试
```bash
# 测试1：获取完整组织架构树
curl -X GET "http://localhost:8380/system/organization/tree"

# 测试2：获取指定单位下的组织架构
curl -X GET "http://localhost:8380/system/organization/tree?deptId=100"
```

### 2. 验证层级关系
检查返回数据中的层级关系是否正确：
- 单位是否作为顶级节点
- 部门是否正确挂在单位下
- 岗位是否正确挂在部门或单位下
- 用户是否正确挂在岗位下或直接挂在部门/单位下

### 3. 验证ID前缀
检查节点ID是否使用了正确的前缀：
- 单位节点：`unit_` + dept_id
- 部门节点：`dept_` + dept_id
- 岗位节点：`post_` + post_id
- 用户节点：`user_` + user_id

### 4. 验证父子关系
检查 `parentId` 字段是否正确：
- 部门的 `parentId` 应该是 `unit_xxx` 或 `dept_xxx`
- 岗位的 `parentId` 应该是 `unit_xxx` 或 `dept_xxx`
- 用户的 `parentId` 应该是 `post_xxx`、`unit_xxx` 或 `dept_xxx`

## 🎯 其他相关接口

### 1. 仅单位树
```bash
curl -X GET "http://localhost:8380/system/organization/unitTree"
```

### 2. 仅部门树
```bash
curl -X GET "http://localhost:8380/system/organization/deptTree"
```

### 3. 单位-部门层级树
```bash
curl -X GET "http://localhost:8380/system/organization/unitDeptTree"
```

## 🐛 常见问题排查

### 1. 数据不显示
- 检查 `del_flag` 字段是否为 '0'（未删除）
- 检查 `status` 字段是否为 '0'（正常状态）
- 检查 `org_type` 字段是否正确设置

### 2. 层级关系错误
- 检查 `parent_id` 字段是否正确
- 检查 `ancestors` 字段是否正确
- 检查 `org_type` 字段是否正确区分单位和部门

### 3. 排序不正确
- 检查 `order_num` 字段
- 验证排序逻辑：单位 > 部门 > 岗位 > 用户

## 📊 数据准备示例

为了测试，确保数据库中有以下结构的数据：

```sql
-- 单位数据
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, org_type, order_num, status, del_flag) 
VALUES (100, 0, '0', '应急管理局', '1', 0, '0', '0');

-- 部门数据
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, org_type, order_num, status, del_flag) 
VALUES (101, 100, '0,100', '综合科', '0', 1, '0', '0');

-- 岗位数据
INSERT INTO sys_post (post_id, post_code, post_name, post_sort, status) 
VALUES (1, '001', '科长', 1, '0');

-- 用户数据
INSERT INTO sys_user (user_id, user_name, nick_name, dept_id, org_id, status, del_flag) 
VALUES (1, 'zhangsan', '张三', 101, 100, '0', '0');

-- 用户岗位关联
INSERT INTO sys_user_post (user_id, post_id) VALUES (1, 1);
```

## ✅ 预期结果

成功调用 `/system/organization/tree` 接口后，应该返回：
1. **完整的四级层级结构**：单位 > 部门 > 岗位 > 用户
2. **正确的节点类型**：unit、dept、post、user
3. **正确的ID前缀**：unit_、dept_、post_、user_
4. **正确的父子关系**：parentId 字段指向正确的父节点
5. **正确的排序**：按类型和排序号排序

这样就实现了完整的单位-部门-岗位-人员四级组织架构树！
