-- 添加org_type字段到sys_dept表
ALTER TABLE `sys_dept` ADD COLUMN `org_type` CHAR(1) DEFAULT '0' COMMENT '组织类型（0部门;1单位）' AFTER `city`;

-- 为现有数据设置默认值（假设现有数据都是单位）
UPDATE `sys_dept` SET `org_type` = '1' WHERE `org_type` IS NULL OR `org_type` = '';

-- 创建组织类型字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) 
VALUES ('组织类型', 'org_type', '0', 'admin', NOW(), '组织类型列表');

-- 创建组织类型字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) 
VALUES 
(1, '部门', '0', 'org_type', '', 'primary', 'Y', '0', 'admin', NOW(), '部门类型'),
(2, '单位', '1', 'org_type', '', 'success', 'N', '0', 'admin', NOW(), '单位类型');
