<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.weather.mapper.WeatherWarningMapper">
    
    <resultMap type="WeatherWarning" id="WeatherWarningResult">
        <result property="warningId"        column="warning_id"        />
        <result property="warningType"      column="warning_type"      />
        <result property="warningLevel"     column="warning_level"     />
        <result property="warningContent"   column="warning_content"   />
        <result property="preventionGuide"  column="prevention_guide"  />
        <result property="affectedRoads"    column="affected_roads"    />
        <result property="issueTime"        column="issue_time"        />
        <result property="expireTime"       column="expire_time"       />
        <result property="status"           column="status"            />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="remark"           column="remark"            />
    </resultMap>

    <resultMap type="WeatherWarning" id="WeatherWarningWithStatsResult" extends="WeatherWarningResult">
        <result property="totalNotifications"       column="total_notifications"       />
        <result property="confirmedNotifications"   column="confirmed_notifications"   />
        <result property="unconfirmedNotifications" column="unconfirmed_notifications" />
        <result property="timeoutNotifications"     column="timeout_notifications"     />
    </resultMap>

    <sql id="selectWeatherWarningVo">
        select warning_id, warning_type, warning_level, warning_content, prevention_guide, 
               affected_roads, issue_time, expire_time, status, create_by, create_time, 
               update_by, update_time, remark 
        from weather_warning
    </sql>

    <select id="selectWeatherWarningList" parameterType="WeatherWarningDTO" resultMap="WeatherWarningResult">
        <include refid="selectWeatherWarningVo"/>
        <where>  
            <if test="warningType != null  and warningType != ''"> and warning_type = #{warningType}</if>
            <if test="warningLevel != null  and warningLevel != ''"> and warning_level = #{warningLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="issueTimeStart != null"> and issue_time &gt;= #{issueTimeStart}</if>
            <if test="issueTimeEnd != null"> and issue_time &lt;= #{issueTimeEnd}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="contentKeyword != null and contentKeyword != ''"> 
                and warning_content like concat('%', #{contentKeyword}, '%')
            </if>
            <if test="regionId != null and regionId != ''">
                and warning_id in (
                    select warning_id from weather_warning_area where region_id = #{regionId}
                )
            </if>
        </where>
        order by issue_time desc
    </select>
    
    <select id="selectWeatherWarningByWarningId" parameterType="String" resultMap="WeatherWarningResult">
        <include refid="selectWeatherWarningVo"/>
        where warning_id = #{warningId}
    </select>

    <select id="selectWeatherWarningWithStats" parameterType="String" resultMap="WeatherWarningWithStatsResult">
        select w.warning_id, w.warning_type, w.warning_level, w.warning_content, w.prevention_guide, 
               w.affected_roads, w.issue_time, w.expire_time, w.status, w.create_by, w.create_time, 
               w.update_by, w.update_time, w.remark,
               COALESCE(n.total_notifications, 0) as total_notifications,
               COALESCE(n.confirmed_notifications, 0) as confirmed_notifications,
               COALESCE(n.unconfirmed_notifications, 0) as unconfirmed_notifications,
               COALESCE(n.timeout_notifications, 0) as timeout_notifications
        from weather_warning w
        left join (
            select warning_id,
                   count(*) as total_notifications,
                   sum(case when confirm_status = '1' then 1 else 0 end) as confirmed_notifications,
                   sum(case when confirm_status = '0' then 1 else 0 end) as unconfirmed_notifications,
                   sum(case when is_timeout = '1' then 1 else 0 end) as timeout_notifications
            from weather_warning_notification
            group by warning_id
        ) n on w.warning_id = n.warning_id
        where w.warning_id = #{warningId}
    </select>

    <select id="selectActiveWarnings" resultMap="WeatherWarningResult">
        <include refid="selectWeatherWarningVo"/>
        where status = '0' 
        and (expire_time is null or expire_time > now())
        order by issue_time desc
    </select>

    <select id="selectExpiringWarnings" parameterType="int" resultMap="WeatherWarningResult">
        <include refid="selectWeatherWarningVo"/>
        where status = '0' 
        and expire_time is not null 
        and expire_time > now()
        and expire_time &lt;= date_add(now(), interval #{minutes} minute)
        order by expire_time asc
    </select>
        
    <insert id="insertWeatherWarning" parameterType="WeatherWarning">
        insert into weather_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warningId != null">warning_id,</if>
            <if test="warningType != null">warning_type,</if>
            <if test="warningLevel != null">warning_level,</if>
            <if test="warningContent != null">warning_content,</if>
            <if test="preventionGuide != null">prevention_guide,</if>
            <if test="affectedRoads != null">affected_roads,</if>
            <if test="issueTime != null">issue_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warningId != null">#{warningId},</if>
            <if test="warningType != null">#{warningType},</if>
            <if test="warningLevel != null">#{warningLevel},</if>
            <if test="warningContent != null">#{warningContent},</if>
            <if test="preventionGuide != null">#{preventionGuide},</if>
            <if test="affectedRoads != null">#{affectedRoads},</if>
            <if test="issueTime != null">#{issueTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateWeatherWarning" parameterType="WeatherWarning">
        update weather_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="warningType != null">warning_type = #{warningType},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="warningContent != null">warning_content = #{warningContent},</if>
            <if test="preventionGuide != null">prevention_guide = #{preventionGuide},</if>
            <if test="affectedRoads != null">affected_roads = #{affectedRoads},</if>
            <if test="issueTime != null">issue_time = #{issueTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where warning_id = #{warningId}
    </update>

    <update id="updateWeatherWarningStatus">
        update weather_warning set status = #{status}, update_time = now() 
        where warning_id = #{warningId}
    </update>

    <delete id="deleteWeatherWarningByWarningId" parameterType="String">
        delete from weather_warning where warning_id = #{warningId}
    </delete>

    <delete id="deleteWeatherWarningByWarningIds" parameterType="String">
        delete from weather_warning where warning_id in 
        <foreach item="warningId" collection="array" open="(" separator="," close=")">
            #{warningId}
        </foreach>
    </delete>

</mapper>
