package com.tocc.weather.service.impl;

import java.util.List;
import java.util.Date;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.exception.ServiceException;
import com.tocc.weather.mapper.WeatherWarningNotificationMapper;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningArea;
import com.tocc.weather.domain.entity.WeatherWarningNotification;
import com.tocc.weather.domain.dto.WeatherWarningNotifyDTO;
import com.tocc.weather.domain.vo.WeatherWarningProgressVO;
import com.tocc.weather.service.IWeatherWarningService;
import com.tocc.weather.service.IWeatherWarningNotificationService;
import com.tocc.weather.utils.WeatherWarningUtils;

/**
 * 气象预警通知记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningNotificationServiceImpl implements IWeatherWarningNotificationService 
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningNotificationServiceImpl.class);

    @Autowired
    private WeatherWarningNotificationMapper weatherWarningNotificationMapper;

    @Autowired
    private IWeatherWarningService weatherWarningService;

    /**
     * 查询气象预警通知记录
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录
     */
    @Override
    public WeatherWarningNotification selectWeatherWarningNotificationByIds(String warningId, Long contactUserId)
    {
        return weatherWarningNotificationMapper.selectWeatherWarningNotificationByIds(warningId, contactUserId);
    }

    /**
     * 查询气象预警通知记录列表
     * 
     * @param warningId 预警ID
     * @return 气象预警通知记录集合
     */
    @Override
    public List<WeatherWarningNotification> selectWeatherWarningNotificationList(String warningId)
    {
        return weatherWarningNotificationMapper.selectWeatherWarningNotificationList(warningId);
    }

    /**
     * 查询用户的预警通知列表
     * 
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录集合
     */
    @Override
    public List<WeatherWarningNotification> selectNotificationsByUserId(Long contactUserId)
    {
        return weatherWarningNotificationMapper.selectNotificationsByUserId(contactUserId);
    }

    /**
     * 新增气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    @Override
    public int insertWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification)
    {
        weatherWarningNotification.setCreateTime(DateUtils.getNowDate());
        return weatherWarningNotificationMapper.insertWeatherWarningNotification(weatherWarningNotification);
    }

    /**
     * 修改气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    @Override
    public int updateWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification)
    {
        weatherWarningNotification.setUpdateTime(DateUtils.getNowDate());
        return weatherWarningNotificationMapper.updateWeatherWarningNotification(weatherWarningNotification);
    }

    /**
     * 批量删除气象预警通知记录
     * 
     * @param warningIds 需要删除的预警ID集合
     * @return 结果
     */
    @Override
    public int deleteWeatherWarningNotificationByWarningIds(String[] warningIds)
    {
        return weatherWarningNotificationMapper.deleteWeatherWarningNotificationByWarningIds(warningIds);
    }

    /**
     * 删除预警的所有通知记录
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    @Override
    public int deleteWeatherWarningNotificationByWarningId(String warningId)
    {
        return weatherWarningNotificationMapper.deleteWeatherWarningNotificationByWarningId(warningId);
    }

    /**
     * 批量创建通知记录
     * 
     * @param warningId 预警ID
     * @param areas 影响区域列表
     * @param notifyTargets 通知对象列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchCreateNotifications(String warningId, List<WeatherWarningArea> areas, List<WeatherWarningNotifyDTO> notifyTargets)
    {
        List<WeatherWarningNotification> notifications = new ArrayList<>();
        
        // 获取预警信息以确定超时时间
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        int timeoutMinutes = WeatherWarningUtils.getTimeoutMinutesByLevel(warning.getWarningLevel());
        
        for (WeatherWarningNotifyDTO target : notifyTargets) {
            WeatherWarningNotification notification = new WeatherWarningNotification();
            notification.setWarningId(warningId);
            notification.setContactUserId(target.getContactUserId());
            notification.setContactUnitName(target.getContactUnitName());
            notification.setContactDeptName(target.getContactDeptName());
            notification.setContactPostName(target.getContactPostName());
            notification.setContactUserName(target.getContactUserName());
            notification.setContactPhone(target.getContactPhone());
            notification.setContactEmail(target.getContactEmail());
            notification.setNotificationTime(new Date());
            notification.setConfirmStatus("0");
            notification.setTimeoutMinutes(timeoutMinutes);
            notification.setIsTimeout("0");
            notification.setCreateTime(new Date());
            
            notifications.add(notification);
        }
        
        if (notifications.isEmpty()) {
            throw new ServiceException("没有有效的通知对象");
        }
        
        // 批量插入通知记录
        int result = weatherWarningNotificationMapper.batchInsertWeatherWarningNotification(notifications);
        
        // 发送通知
        sendNotifications(notifications);
        
        return result;
    }

    /**
     * 确认预警通知
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param confirmUserId 确认人ID
     * @param confirmUserName 确认人姓名
     * @return 结果
     */
    @Override
    public int confirmNotification(String warningId, Long contactUserId, Long confirmUserId, String confirmUserName)
    {
        return weatherWarningNotificationMapper.confirmNotification(warningId, contactUserId, confirmUserId, confirmUserName);
    }

    /**
     * 查询预警通知进展
     * 
     * @param warningId 预警ID
     * @return 通知进展列表
     */
    @Override
    public List<WeatherWarningProgressVO> selectNotificationProgress(String warningId)
    {
        return weatherWarningNotificationMapper.selectNotificationProgress(warningId);
    }

    /**
     * 查询超时的通知记录
     * 
     * @return 超时通知记录列表
     */
    @Override
    public List<WeatherWarningNotification> selectTimeoutNotifications()
    {
        return weatherWarningNotificationMapper.selectTimeoutNotifications();
    }

    /**
     * 查询未确认的通知记录
     * 
     * @param warningId 预警ID
     * @return 未确认通知记录列表
     */
    @Override
    public List<WeatherWarningNotification> selectUnconfirmedNotifications(String warningId)
    {
        return weatherWarningNotificationMapper.selectUnconfirmedNotifications(warningId);
    }

    /**
     * 更新通知超时状态
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param alarmId 告警ID
     * @return 结果
     */
    @Override
    public int updateNotificationTimeout(String warningId, Long contactUserId, String alarmId)
    {
        return weatherWarningNotificationMapper.updateNotificationTimeout(warningId, contactUserId, alarmId);
    }

    /**
     * 催办未确认通知
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    @Override
    public int remindUnconfirmedNotifications(String warningId)
    {
        List<WeatherWarningNotification> unconfirmedList = selectUnconfirmedNotifications(warningId);
        
        if (unconfirmedList.isEmpty()) {
            return 0;
        }
        
        // 获取预警信息
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        
        // 发送催办通知
        for (WeatherWarningNotification notification : unconfirmedList) {
            sendSingleNotification(notification, warning);
        }
        
        return unconfirmedList.size();
    }

    /**
     * 发送通知
     * 
     * @param notifications 通知记录列表
     */
    @Override
    public void sendNotifications(List<WeatherWarningNotification> notifications)
    {
        if (notifications == null || notifications.isEmpty()) {
            return;
        }
        
        // 获取预警信息
        String warningId = notifications.get(0).getWarningId();
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        
        for (WeatherWarningNotification notification : notifications) {
            try {
                sendSingleNotification(notification, warning);
            } catch (Exception e) {
                log.error("发送通知失败：{}", notification.getContactUserName(), e);
                // 发送失败只记录日志，不影响主流程
            }
        }
    }

    /**
     * 发送单个通知
     * 
     * @param notification 通知记录
     * @param warning 预警信息
     */
    @Override
    public void sendSingleNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        try {
            // 1. 系统内通知
            sendSystemNotification(notification, warning);
            
            // 2. 短信通知（如果有手机号）
            if (StringUtils.isNotEmpty(notification.getContactPhone())) {
                sendSmsNotification(notification, warning);
            }
            
            // 3. 邮件通知（如果有邮箱）
            if (StringUtils.isNotEmpty(notification.getContactEmail())) {
                sendEmailNotification(notification, warning);
            }
            
            log.info("预警通知发送成功：{} -> {}", warning.getWarningId(), notification.getContactUserName());
            
        } catch (Exception e) {
            log.error("发送预警通知失败：{} -> {}", warning.getWarningId(), notification.getContactUserName(), e);
            throw e;
        }
    }

    /**
     * 发送系统内通知
     */
    private void sendSystemNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        // TODO: 调用系统通知服务
        log.info("发送系统通知：{} -> {}", warning.getWarningId(), notification.getContactUserName());
    }

    /**
     * 发送短信通知
     */
    private void sendSmsNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        String content = String.format(
            "【气象预警】%s发布%s，发布时间：%s。请及时确认并采取相应措施。",
            getWarningTypeLabel(warning.getWarningType()),
            getWarningLevelLabel(warning.getWarningLevel()),
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getIssueTime())
        );
        
        // TODO: 调用短信服务
        log.info("发送短信通知：{} -> {} ({})", warning.getWarningId(), notification.getContactUserName(), notification.getContactPhone());
    }

    /**
     * 发送邮件通知
     */
    private void sendEmailNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        String subject = String.format("气象预警通知 - %s%s", getWarningTypeLabel(warning.getWarningType()), getWarningLevelLabel(warning.getWarningLevel()));
        String content = buildEmailContent(warning, notification);
        
        // TODO: 调用邮件服务
        log.info("发送邮件通知：{} -> {} ({})", warning.getWarningId(), notification.getContactUserName(), notification.getContactEmail());
    }

    /**
     * 构建邮件内容
     */
    private String buildEmailContent(WeatherWarning warning, WeatherWarningNotification notification)
    {
        StringBuilder content = new StringBuilder();
        content.append("尊敬的").append(notification.getContactUserName()).append("：\n\n");
        content.append("您收到一条气象预警通知，详情如下：\n\n");
        content.append("预警类型：").append(getWarningTypeLabel(warning.getWarningType())).append("\n");
        content.append("预警等级：").append(getWarningLevelLabel(warning.getWarningLevel())).append("\n");
        content.append("发布时间：").append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getIssueTime())).append("\n");
        if (warning.getExpireTime() != null) {
            content.append("失效时间：").append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getExpireTime())).append("\n");
        }
        content.append("\n预警内容：\n").append(warning.getWarningContent()).append("\n");
        if (StringUtils.isNotEmpty(warning.getPreventionGuide())) {
            content.append("\n防御指南：\n").append(warning.getPreventionGuide()).append("\n");
        }
        content.append("\n请及时确认此预警信息并采取相应措施。");
        
        return content.toString();
    }

    /**
     * 获取预警类型标签
     */
    private String getWarningTypeLabel(String warningType)
    {
        return WeatherWarningUtils.getWarningTypeName(warningType);
    }

    /**
     * 获取预警等级标签
     */
    private String getWarningLevelLabel(String warningLevel)
    {
        return WeatherWarningUtils.getWarningLevelName(warningLevel);
    }
}
