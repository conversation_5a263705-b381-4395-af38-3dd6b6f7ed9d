package com.tocc.weather.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.service.IAlarmService;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;

/**
 * 气象预警告警服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningAlarmService
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningAlarmService.class);

    @Autowired
    private IAlarmService alarmService;

    /**
     * 创建气象预警告警
     *
     * @param createDTO 预警创建DTO
     * @param warning 预警信息
     */
    public void createWeatherWarningAlarm(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        try {
            AlarmInfoDTO alarmInfo = new AlarmInfoDTO();

            // 设置告警标题：xx行政辖区发生xx预警
            String alarmTitle = buildAlarmTitle(createDTO, warning);
            alarmInfo.setAlarmTitle(alarmTitle);

            // 设置告警类型和级别
            alarmInfo.setAlarmType("3"); // 气象告警
            alarmInfo.setAlarmSubtype("7"); // 气象告警子类型
            alarmInfo.setAlarmLevel(mapWarningLevelToAlarmLevel(warning.getWarningLevel()));

            // 设置详细的告警内容
            String alarmContent = buildAlarmContent(createDTO, warning);
            alarmInfo.setAlarmContent(alarmContent);

            // 设置关联信息
            alarmInfo.setSourceId(warning.getWarningId());
            alarmInfo.setSourceType("weather_warning");

            // 设置组织信息（使用当前用户的部门信息）
            Long currentDeptId = SecurityUtils.getDeptId();
            alarmInfo.setOrgId(currentDeptId != null ? currentDeptId.toString() : "");
            alarmInfo.setOrgName("气象预警发布部门");

            // 调用告警服务创建告警
            alarmService.insertAlarmInfo(alarmInfo);

            log.info("创建气象预警告警成功：{}", warning.getWarningId());

        } catch (Exception e) {
            // 告警创建失败不影响主业务流程，只记录日志
            log.error("创建气象预警告警失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建告警标题
     */
    private String buildAlarmTitle(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        // 获取第一个影响区域作为标题
        String areaName = "未知区域";
        if (createDTO.getAffectedAreas() != null && !createDTO.getAffectedAreas().isEmpty()) {
            areaName = createDTO.getAffectedAreas().get(0).getRegionName();
        }

        String warningTypeName = getWarningTypeLabel(warning.getWarningType());
        String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());

        return String.format("%s发布%s%s", areaName, warningTypeName, warningLevelName);
    }

    /**
     * 构建告警内容
     */
    private String buildAlarmContent(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        StringBuilder content = new StringBuilder();
        content.append("气象预警信息\n\n");
        content.append("预警类型：").append(getWarningTypeLabel(warning.getWarningType())).append("\n");
        content.append("预警等级：").append(getWarningLevelLabel(warning.getWarningLevel())).append("\n");
        content.append("发布时间：").append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getIssueTime())).append("\n");

        if (warning.getExpireTime() != null) {
            content.append("失效时间：").append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getExpireTime())).append("\n");
        }

        // 影响区域
        if (createDTO.getAffectedAreas() != null && !createDTO.getAffectedAreas().isEmpty()) {
            content.append("影响区域：");
            for (int i = 0; i < createDTO.getAffectedAreas().size(); i++) {
                if (i > 0) content.append("、");
                content.append(createDTO.getAffectedAreas().get(i).getRegionName());
            }
            content.append("\n");
        }

        content.append("\n预警内容：\n").append(warning.getWarningContent());

        if (warning.getPreventionGuide() != null && !warning.getPreventionGuide().trim().isEmpty()) {
            content.append("\n\n防御指南：\n").append(warning.getPreventionGuide());
        }

        return content.toString();
    }

    /**
     * 将预警等级映射为告警等级
     */
    private String mapWarningLevelToAlarmLevel(String warningLevel)
    {
        // 预警等级映射到告警等级
        switch (warningLevel) {
            case "8": return "8"; // 红色预警 -> 红色告警
            case "7": return "7"; // 橙色预警 -> 橙色告警
            case "6": return "6"; // 黄色预警 -> 黄色告警
            case "5": return "5"; // 蓝色预警 -> 蓝色告警
            default: return "6"; // 默认黄色告警
        }
    }

    /**
     * 获取预警类型标签
     */
    private String getWarningTypeLabel(String warningType)
    {
        switch (warningType) {
            case "1": return "暴雨预警";
            case "2": return "台风预警";
            case "3": return "雷电预警";
            case "4": return "大风预警";
            case "5": return "冰雹预警";
            case "6": return "高温预警";
            case "7": return "寒潮预警";
            case "8": return "大雾预警";
            case "9": return "道路结冰预警";
            case "10": return "霜冻预警";
            default: return "未知预警";
        }
    }

    /**
     * 获取预警等级标签
     */
    private String getWarningLevelLabel(String warningLevel)
    {
        switch (warningLevel) {
            case "5": return "蓝色预警";
            case "6": return "黄色预警";
            case "7": return "橙色预警";
            case "8": return "红色预警";
            default: return "未知等级";
        }
    }
}
