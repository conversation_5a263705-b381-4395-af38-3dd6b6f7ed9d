package com.tocc.weather.service;

import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.service.IAlarmService;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;

/**
 * 气象预警告警服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningAlarmService
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningAlarmService.class);

    @Autowired
    private IAlarmService alarmService;

    /**
     * 创建气象预警告警
     *
     * @param createDTO 预警创建DTO
     * @param warning 预警信息
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void createWeatherWarningAlarm(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        try {
            AlarmInfoDTO alarmInfo = new AlarmInfoDTO();

            // 设置告警ID（必填）
            alarmInfo.setAlarmId(IdUtils.fastUUID());

            // 设置告警标题：xx行政辖区发生xx预警
            String alarmTitle = buildAlarmTitle(createDTO, warning);
            alarmInfo.setAlarmTitle(alarmTitle);

            // 设置告警类型和级别
            alarmInfo.setAlarmType("3"); // 气象告警
            alarmInfo.setAlarmSubtype("7"); // 气象告警子类型
            alarmInfo.setAlarmLevel(mapWarningLevelToAlarmLevel(warning.getWarningLevel()));

            // 设置详细的告警内容
            String alarmContent = buildAlarmContent(createDTO, warning);
            alarmInfo.setAlarmContent(alarmContent);

            // 设置关联信息
            alarmInfo.setSourceId(warning.getWarningId());
            alarmInfo.setSourceType("weather_warning");

            // 设置组织信息（使用当前用户的部门信息）
            Long currentDeptId = SecurityUtils.getDeptId();
            alarmInfo.setOrgId(currentDeptId != null ? currentDeptId.toString() : "100");
            alarmInfo.setOrgName("气象预警发布部门");

            // 设置行政区划ID（取第一个影响区域）
            if (createDTO.getAffectedAreas() != null && !createDTO.getAffectedAreas().isEmpty()) {
                String firstRegionId = createDTO.getAffectedAreas().get(0).getRegionId();
                alarmInfo.setAdministrativeAreaId(firstRegionId);
            } else {
                // 如果没有区域信息，使用默认值
                alarmInfo.setAdministrativeAreaId("450000"); // 广西壮族自治区
            }

            // 设置告警状态和时间
            alarmInfo.setStatus("0"); // 未处理
            alarmInfo.setAlarmTime(warning.getIssueTime()); // 使用预警发布时间作为告警时间

            // 设置创建信息
            alarmInfo.setCreateBy(SecurityUtils.getUsername());
            alarmInfo.setCreateTime(new Date());

            // 调用告警服务创建告警
            log.info("准备创建气象预警告警，告警ID：{}，预警ID：{}", alarmInfo.getAlarmId(), warning.getWarningId());
            log.info("告警信息：标题={}, 类型={}, 子类型={}, 等级={}, 组织ID={}, 区域ID={}",
                alarmInfo.getAlarmTitle(), alarmInfo.getAlarmType(), alarmInfo.getAlarmSubtype(),
                alarmInfo.getAlarmLevel(), alarmInfo.getOrgId(), alarmInfo.getAdministrativeAreaId());

            alarmService.insertAlarmInfo(alarmInfo);

            log.info("创建气象预警告警成功：告警ID={}，预警ID={}", alarmInfo.getAlarmId(), warning.getWarningId());

        } catch (Exception e) {
            // 告警创建失败不影响主业务流程，只记录日志
            log.error("创建气象预警告警失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建告警标题
     */
    private String buildAlarmTitle(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        String warningTypeName = getWarningTypeLabel(warning.getWarningType());
        String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());

        return String.format("发布%s%s", warningTypeName, warningLevelName);
    }

    /**
     * 构建告警内容
     */
    private String buildAlarmContent(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        StringBuilder content = new StringBuilder();

        // 发布时间（年月日时分格式）
        String issueTimeStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分", warning.getIssueTime());
        content.append(issueTimeStr).append("，");

        // 影响区域列表
        if (createDTO.getAffectedAreas() != null && !createDTO.getAffectedAreas().isEmpty()) {
            for (int i = 0; i < createDTO.getAffectedAreas().size(); i++) {
                if (i > 0) content.append("、");
                content.append(createDTO.getAffectedAreas().get(i).getRegionName());
            }
            if (createDTO.getAffectedAreas().size() > 1) {
                content.append("等").append(createDTO.getAffectedAreas().size()).append("个区域");
            }
        } else {
            content.append("相关区域");
        }

        // 预警信息
        String warningTypeName = getWarningTypeLabel(warning.getWarningType());
        String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());
        content.append("发布").append(warningTypeName).append(warningLevelName);

        // 失效时间
        if (warning.getExpireTime() != null) {
            String expireTimeStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分", warning.getExpireTime());
            content.append("，失效时间：").append(expireTimeStr);
        }

        // 预警内容
        content.append("\n\n预警内容：\n").append(warning.getWarningContent());

        // 防御指南
        if (warning.getPreventionGuide() != null && !warning.getPreventionGuide().trim().isEmpty()) {
            content.append("\n\n防御指南：\n").append(warning.getPreventionGuide());
        }

        return content.toString();
    }

    /**
     * 将预警等级映射为告警等级
     */
    private String mapWarningLevelToAlarmLevel(String warningLevel)
    {
        // 预警等级映射到告警等级
        switch (warningLevel) {
            case "8": return "8"; // 红色预警 -> 红色告警
            case "7": return "7"; // 橙色预警 -> 橙色告警
            case "6": return "6"; // 黄色预警 -> 黄色告警
            case "5": return "5"; // 蓝色预警 -> 蓝色告警
            default: return "6"; // 默认黄色告警
        }
    }

    /**
     * 获取预警类型标签
     */
    private String getWarningTypeLabel(String warningType)
    {
        switch (warningType) {
            case "1": return "暴雨预警";
            case "2": return "台风预警";
            case "3": return "雷电预警";
            case "4": return "大风预警";
            case "5": return "冰雹预警";
            case "6": return "高温预警";
            case "7": return "寒潮预警";
            case "8": return "大雾预警";
            case "9": return "道路结冰预警";
            case "10": return "霜冻预警";
            default: return "未知预警";
        }
    }

    /**
     * 获取预警等级标签
     */
    private String getWarningLevelLabel(String warningLevel)
    {
        switch (warningLevel) {
            case "5": return "蓝色预警";
            case "6": return "黄色预警";
            case "7": return "橙色预警";
            case "8": return "红色预警";
            default: return "未知等级";
        }
    }
}
