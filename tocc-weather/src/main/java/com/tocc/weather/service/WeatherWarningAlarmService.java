package com.tocc.weather.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.emer.domain.dto.AlarmInfoDTO;
import com.tocc.emer.service.IAlarmInfoService;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningNotification;

/**
 * 气象预警告警服务
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningAlarmService 
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningAlarmService.class);

    @Autowired
    private IAlarmInfoService alarmInfoService;

    @Autowired
    private IWeatherWarningService weatherWarningService;

    /**
     * 创建气象预警超时告警
     * 
     * @param notification 通知记录
     * @param warning 预警信息
     * @return 告警ID
     */
    public String createTimeoutAlarm(WeatherWarningNotification notification, WeatherWarning warning) 
    {
        try {
            AlarmInfoDTO alarmDto = new AlarmInfoDTO();
            
            // 设置告警基本信息
            alarmDto.setAlarmId(IdUtils.fastUUID());
            alarmDto.setAlarmType(3); // 气象告警
            alarmDto.setAlarmSubtype(7); // 气象告警子类型
            alarmDto.setSubtype("TIMEOUT"); // 具体种类：超时
            
            // 设置告警内容
            alarmDto.setTitle(buildTimeoutAlarmTitle(warning, notification));
            alarmDto.setContent(buildTimeoutAlarmContent(notification, warning));
            
            // 设置组织信息
            alarmDto.setOrgId(getOrgIdFromUnitName(notification.getContactUnitName()));
            alarmDto.setOrgName(notification.getContactUnitName());
            
            // 设置关联信息
            alarmDto.setSourceType("WEATHER_WARNING");
            alarmDto.setSourceId(notification.getWarningId());
            
            // 创建告警
            String alarmId = alarmInfoService.createAlarm(alarmDto);
            
            log.info("创建气象预警超时告警成功：{} -> {}", warning.getWarningId(), notification.getContactUserName());
            
            return alarmId;
            
        } catch (Exception e) {
            log.error("创建气象预警超时告警失败：{} -> {}", warning.getWarningId(), notification.getContactUserName(), e);
            throw e;
        }
    }

    /**
     * 创建预警升级告警
     * 
     * @param warning 预警信息
     * @param oldLevel 原等级
     * @param newLevel 新等级
     * @return 告警ID
     */
    public String createUpgradeAlarm(WeatherWarning warning, String oldLevel, String newLevel) 
    {
        try {
            AlarmInfoDTO alarmDto = new AlarmInfoDTO();
            
            alarmDto.setAlarmId(IdUtils.fastUUID());
            alarmDto.setAlarmType(3);
            alarmDto.setAlarmSubtype(7);
            alarmDto.setSubtype("UPGRADE");
            
            alarmDto.setTitle(buildUpgradeAlarmTitle(warning));
            alarmDto.setContent(buildUpgradeAlarmContent(warning, oldLevel, newLevel));
            
            // 预警升级告警发送给创建者所在单位
            alarmDto.setOrgId(getOrgIdFromCreateBy(warning.getCreateBy()));
            alarmDto.setOrgName(getOrgNameFromCreateBy(warning.getCreateBy()));
            
            alarmDto.setSourceType("WEATHER_WARNING");
            alarmDto.setSourceId(warning.getWarningId());
            
            String alarmId = alarmInfoService.createAlarm(alarmDto);
            
            log.info("创建气象预警升级告警成功：{}", warning.getWarningId());
            
            return alarmId;
            
        } catch (Exception e) {
            log.error("创建气象预警升级告警失败：{}", warning.getWarningId(), e);
            throw e;
        }
    }

    /**
     * 创建通知发送失败告警
     * 
     * @param warning 预警信息
     * @param failedContacts 发送失败的联系人列表
     * @return 告警ID
     */
    public String createSendFailedAlarm(WeatherWarning warning, String failedContacts) 
    {
        try {
            AlarmInfoDTO alarmDto = new AlarmInfoDTO();
            
            alarmDto.setAlarmId(IdUtils.fastUUID());
            alarmDto.setAlarmType(3);
            alarmDto.setAlarmSubtype(7);
            alarmDto.setSubtype("SEND_FAILED");
            
            alarmDto.setTitle(buildSendFailedAlarmTitle(warning));
            alarmDto.setContent(buildSendFailedAlarmContent(warning, failedContacts));
            
            // 发送失败告警发送给创建者所在单位
            alarmDto.setOrgId(getOrgIdFromCreateBy(warning.getCreateBy()));
            alarmDto.setOrgName(getOrgNameFromCreateBy(warning.getCreateBy()));
            
            alarmDto.setSourceType("WEATHER_WARNING");
            alarmDto.setSourceId(warning.getWarningId());
            
            String alarmId = alarmInfoService.createAlarm(alarmDto);
            
            log.info("创建气象预警发送失败告警成功：{}", warning.getWarningId());
            
            return alarmId;
            
        } catch (Exception e) {
            log.error("创建气象预警发送失败告警失败：{}", warning.getWarningId(), e);
            throw e;
        }
    }

    /**
     * 构建超时告警标题
     */
    private String buildTimeoutAlarmTitle(WeatherWarning warning, WeatherWarningNotification notification) 
    {
        return String.format("气象预警确认超时 - %s", notification.getContactUnitName());
    }

    /**
     * 构建超时告警内容
     */
    private String buildTimeoutAlarmContent(WeatherWarningNotification notification, WeatherWarning warning) 
    {
        StringBuilder content = new StringBuilder();
        content.append("气象预警确认超时告警\n\n");
        content.append("预警信息：").append(getWarningTypeLabel(warning.getWarningType()))
               .append(" ").append(getWarningLevelLabel(warning.getWarningLevel())).append("\n");
        content.append("发布时间：").append(DateUtils.formatDateTime(warning.getIssueTime())).append("\n");
        content.append("超时单位：").append(notification.getContactUnitName()).append("\n");
        content.append("联系人：").append(notification.getContactUserName()).append("\n");
        content.append("联系电话：").append(notification.getContactPhone()).append("\n");
        content.append("通知时间：").append(DateUtils.formatDateTime(notification.getNotificationTime())).append("\n");
        content.append("超时时长：").append(notification.getTimeoutMinutes()).append("分钟\n\n");
        content.append("请及时联系相关单位确认预警信息。");
        
        return content.toString();
    }

    /**
     * 构建升级告警标题
     */
    private String buildUpgradeAlarmTitle(WeatherWarning warning) 
    {
        return String.format("气象预警等级升级 - %s", getWarningTypeLabel(warning.getWarningType()));
    }

    /**
     * 构建升级告警内容
     */
    private String buildUpgradeAlarmContent(WeatherWarning warning, String oldLevel, String newLevel) 
    {
        StringBuilder content = new StringBuilder();
        content.append("气象预警等级升级告警\n\n");
        content.append("预警类型：").append(getWarningTypeLabel(warning.getWarningType())).append("\n");
        content.append("发布时间：").append(DateUtils.formatDateTime(warning.getIssueTime())).append("\n");
        content.append("等级变化：").append(getWarningLevelLabel(oldLevel))
               .append(" → ").append(getWarningLevelLabel(newLevel)).append("\n");
        content.append("预警内容：").append(warning.getWarningContent()).append("\n\n");
        content.append("请关注预警等级变化，及时调整应对措施。");
        
        return content.toString();
    }

    /**
     * 构建发送失败告警标题
     */
    private String buildSendFailedAlarmTitle(WeatherWarning warning) 
    {
        return String.format("气象预警通知发送失败 - %s", getWarningTypeLabel(warning.getWarningType()));
    }

    /**
     * 构建发送失败告警内容
     */
    private String buildSendFailedAlarmContent(WeatherWarning warning, String failedContacts) 
    {
        StringBuilder content = new StringBuilder();
        content.append("气象预警通知发送失败告警\n\n");
        content.append("预警信息：").append(getWarningTypeLabel(warning.getWarningType()))
               .append(" ").append(getWarningLevelLabel(warning.getWarningLevel())).append("\n");
        content.append("发布时间：").append(DateUtils.formatDateTime(warning.getIssueTime())).append("\n");
        content.append("发送失败联系人：").append(failedContacts).append("\n\n");
        content.append("请检查通知渠道并重新发送。");
        
        return content.toString();
    }

    /**
     * 根据单位名称获取单位ID
     */
    private Long getOrgIdFromUnitName(String unitName) 
    {
        // TODO: 实现根据单位名称查找单位ID的逻辑
        // 可以调用系统部门服务查询
        return 100L; // 临时返回默认值
    }

    /**
     * 根据创建者获取单位ID
     */
    private Long getOrgIdFromCreateBy(String createBy) 
    {
        // TODO: 实现根据创建者查找其所在单位ID的逻辑
        return 100L; // 临时返回默认值
    }

    /**
     * 根据创建者获取单位名称
     */
    private String getOrgNameFromCreateBy(String createBy) 
    {
        // TODO: 实现根据创建者查找其所在单位名称的逻辑
        return "应急管理局"; // 临时返回默认值
    }

    /**
     * 获取预警类型标签
     */
    private String getWarningTypeLabel(String warningType) 
    {
        // TODO: 从字典服务获取标签
        switch (warningType) {
            case "1": return "暴雨预警";
            case "2": return "台风预警";
            case "3": return "雷电预警";
            case "4": return "大风预警";
            case "5": return "冰雹预警";
            case "6": return "高温预警";
            case "7": return "寒潮预警";
            case "8": return "大雾预警";
            case "9": return "道路结冰预警";
            case "10": return "霜冻预警";
            default: return "未知预警";
        }
    }

    /**
     * 获取预警等级标签
     */
    private String getWarningLevelLabel(String warningLevel) 
    {
        // TODO: 从字典服务获取标签
        switch (warningLevel) {
            case "5": return "蓝色预警";
            case "6": return "黄色预警";
            case "7": return "橙色预警";
            case "8": return "红色预警";
            default: return "未知等级";
        }
    }
}
