package com.tocc.weather.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 气象预警详情VO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningVO
{
    /** 预警ID */
    private String warningId;

    /** 预警类型 */
    private String warningType;

    /** 预警类型标签 */
    private String warningTypeLabel;

    /** 预警等级 */
    private String warningLevel;

    /** 预警等级标签 */
    private String warningLevelLabel;

    /** 预警内容 */
    private String warningContent;

    /** 防御指南 */
    private String preventionGuide;

    /** 受影响道路 */
    private String affectedRoads;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 状态 */
    private String status;

    /** 状态标签 */
    private String statusLabel;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建者 */
    private String createBy;

    /** 影响区域信息 */
    private List<WeatherWarningAreaVO> affectedAreas;

    /** 影响区域描述 */
    private String affectedAreasDesc;

    /** 通知统计信息 */
    private Integer totalNotifications;
    private Integer confirmedNotifications;
    private Integer unconfirmedNotifications;
    private Integer timeoutNotifications;

    public String getWarningId() 
    {
        return warningId;
    }

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public String getWarningType() 
    {
        return warningType;
    }

    public void setWarningType(String warningType) 
    {
        this.warningType = warningType;
    }

    public String getWarningTypeLabel() 
    {
        return warningTypeLabel;
    }

    public void setWarningTypeLabel(String warningTypeLabel) 
    {
        this.warningTypeLabel = warningTypeLabel;
    }

    public String getWarningLevel() 
    {
        return warningLevel;
    }

    public void setWarningLevel(String warningLevel) 
    {
        this.warningLevel = warningLevel;
    }

    public String getWarningLevelLabel() 
    {
        return warningLevelLabel;
    }

    public void setWarningLevelLabel(String warningLevelLabel) 
    {
        this.warningLevelLabel = warningLevelLabel;
    }

    public String getWarningContent() 
    {
        return warningContent;
    }

    public void setWarningContent(String warningContent) 
    {
        this.warningContent = warningContent;
    }

    public String getPreventionGuide() 
    {
        return preventionGuide;
    }

    public void setPreventionGuide(String preventionGuide) 
    {
        this.preventionGuide = preventionGuide;
    }

    public String getAffectedRoads() 
    {
        return affectedRoads;
    }

    public void setAffectedRoads(String affectedRoads) 
    {
        this.affectedRoads = affectedRoads;
    }

    public Date getIssueTime() 
    {
        return issueTime;
    }

    public void setIssueTime(Date issueTime) 
    {
        this.issueTime = issueTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatusLabel() 
    {
        return statusLabel;
    }

    public void setStatusLabel(String statusLabel) 
    {
        this.statusLabel = statusLabel;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }

    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public String getCreateBy() 
    {
        return createBy;
    }

    public void setCreateBy(String createBy) 
    {
        this.createBy = createBy;
    }

    public List<WeatherWarningAreaVO> getAffectedAreas() 
    {
        return affectedAreas;
    }

    public void setAffectedAreas(List<WeatherWarningAreaVO> affectedAreas) 
    {
        this.affectedAreas = affectedAreas;
    }

    public String getAffectedAreasDesc() 
    {
        return affectedAreasDesc;
    }

    public void setAffectedAreasDesc(String affectedAreasDesc) 
    {
        this.affectedAreasDesc = affectedAreasDesc;
    }

    public Integer getTotalNotifications() 
    {
        return totalNotifications;
    }

    public void setTotalNotifications(Integer totalNotifications) 
    {
        this.totalNotifications = totalNotifications;
    }

    public Integer getConfirmedNotifications() 
    {
        return confirmedNotifications;
    }

    public void setConfirmedNotifications(Integer confirmedNotifications) 
    {
        this.confirmedNotifications = confirmedNotifications;
    }

    public Integer getUnconfirmedNotifications() 
    {
        return unconfirmedNotifications;
    }

    public void setUnconfirmedNotifications(Integer unconfirmedNotifications) 
    {
        this.unconfirmedNotifications = unconfirmedNotifications;
    }

    public Integer getTimeoutNotifications() 
    {
        return timeoutNotifications;
    }

    public void setTimeoutNotifications(Integer timeoutNotifications) 
    {
        this.timeoutNotifications = timeoutNotifications;
    }

    @Override
    public String toString() {
        return "WeatherWarningVO{" +
                "warningId='" + warningId + '\'' +
                ", warningType='" + warningType + '\'' +
                ", warningLevel='" + warningLevel + '\'' +
                ", warningContent='" + warningContent + '\'' +
                ", issueTime=" + issueTime +
                ", status='" + status + '\'' +
                '}';
    }
}
