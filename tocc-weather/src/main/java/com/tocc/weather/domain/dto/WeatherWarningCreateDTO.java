package com.tocc.weather.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 创建气象预警DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningCreateDTO
{
    /** 预警类型 */
    @NotBlank(message = "预警类型不能为空")
    private String warningType;

    /** 预警等级 */
    @NotBlank(message = "预警等级不能为空")
    private String warningLevel;

    /** 预警内容 */
    @NotBlank(message = "预警内容不能为空")
    private String warningContent;

    /** 防御指南 */
    private String preventionGuide;

    /** 受影响道路 */
    private String affectedRoads;

    /** 发布时间 */
    @NotNull(message = "发布时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 影响区域列表 */
    @NotEmpty(message = "请选择影响区域")
    private List<WeatherWarningAreaDTO> affectedAreas;

    /** 通知对象列表 */
    @NotEmpty(message = "请选择通知对象")
    private List<WeatherWarningNotifyDTO> notifyTargets;

    public String getWarningType() 
    {
        return warningType;
    }

    public void setWarningType(String warningType) 
    {
        this.warningType = warningType;
    }

    public String getWarningLevel() 
    {
        return warningLevel;
    }

    public void setWarningLevel(String warningLevel) 
    {
        this.warningLevel = warningLevel;
    }

    public String getWarningContent() 
    {
        return warningContent;
    }

    public void setWarningContent(String warningContent) 
    {
        this.warningContent = warningContent;
    }

    public String getPreventionGuide() 
    {
        return preventionGuide;
    }

    public void setPreventionGuide(String preventionGuide) 
    {
        this.preventionGuide = preventionGuide;
    }

    public String getAffectedRoads() 
    {
        return affectedRoads;
    }

    public void setAffectedRoads(String affectedRoads) 
    {
        this.affectedRoads = affectedRoads;
    }

    public Date getIssueTime() 
    {
        return issueTime;
    }

    public void setIssueTime(Date issueTime) 
    {
        this.issueTime = issueTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public List<WeatherWarningAreaDTO> getAffectedAreas() 
    {
        return affectedAreas;
    }

    public void setAffectedAreas(List<WeatherWarningAreaDTO> affectedAreas) 
    {
        this.affectedAreas = affectedAreas;
    }

    public List<WeatherWarningNotifyDTO> getNotifyTargets() 
    {
        return notifyTargets;
    }

    public void setNotifyTargets(List<WeatherWarningNotifyDTO> notifyTargets) 
    {
        this.notifyTargets = notifyTargets;
    }

    @Override
    public String toString() {
        return "WeatherWarningCreateDTO{" +
                "warningType='" + warningType + '\'' +
                ", warningLevel='" + warningLevel + '\'' +
                ", warningContent='" + warningContent + '\'' +
                ", preventionGuide='" + preventionGuide + '\'' +
                ", affectedRoads='" + affectedRoads + '\'' +
                ", issueTime=" + issueTime +
                ", expireTime=" + expireTime +
                ", affectedAreas=" + affectedAreas +
                ", notifyTargets=" + notifyTargets +
                '}';
    }
}
