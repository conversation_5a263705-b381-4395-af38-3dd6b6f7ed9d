package com.tocc.weather.domain.dto;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 气象预警通知对象DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningNotifyTargetDTO 
{
    /** 联系人用户ID */
    @NotNull(message = "联系人用户ID不能为空")
    private Long contactUserId;

    /** 联系人姓名 */
    @NotBlank(message = "联系人姓名不能为空")
    private String contactUserName;

    /** 联系人单位ID */
    @NotBlank(message = "联系人单位ID不能为空")
    private String contactUnitId;

    /** 联系人单位名称 */
    @NotBlank(message = "联系人单位名称不能为空")
    private String contactUnitName;

    /** 联系人部门名称 */
    private String contactDeptName;

    /** 联系人岗位名称 */
    private String contactPostName;

    /** 联系人电话 */
    private String contactPhone;

    /** 联系人邮箱 */
    private String contactEmail;

    public Long getContactUserId() 
    {
        return contactUserId;
    }

    public void setContactUserId(Long contactUserId) 
    {
        this.contactUserId = contactUserId;
    }

    public String getContactUserName() 
    {
        return contactUserName;
    }

    public void setContactUserName(String contactUserName) 
    {
        this.contactUserName = contactUserName;
    }

    public String getContactUnitId() 
    {
        return contactUnitId;
    }

    public void setContactUnitId(String contactUnitId) 
    {
        this.contactUnitId = contactUnitId;
    }

    public String getContactUnitName() 
    {
        return contactUnitName;
    }

    public void setContactUnitName(String contactUnitName) 
    {
        this.contactUnitName = contactUnitName;
    }

    public String getContactDeptName() 
    {
        return contactDeptName;
    }

    public void setContactDeptName(String contactDeptName) 
    {
        this.contactDeptName = contactDeptName;
    }

    public String getContactPostName() 
    {
        return contactPostName;
    }

    public void setContactPostName(String contactPostName) 
    {
        this.contactPostName = contactPostName;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    @Override
    public String toString() {
        return "WeatherWarningNotifyTargetDTO{" +
                "contactUserId=" + contactUserId +
                ", contactUserName='" + contactUserName + '\'' +
                ", contactUnitId='" + contactUnitId + '\'' +
                ", contactUnitName='" + contactUnitName + '\'' +
                ", contactDeptName='" + contactDeptName + '\'' +
                ", contactPostName='" + contactPostName + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                '}';
    }
}
