package com.tocc.weather.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import java.util.Date;
import java.util.List;

/**
 * 气象预警信息对象 weather_warning
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarning extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 预警ID */
    private String warningId;

    /** 预警类型 */
    @Excel(name = "预警类型")
    private String warningType;

    /** 预警等级 */
    @Excel(name = "预警等级")
    private String warningLevel;

    /** 预警内容 */
    @Excel(name = "预警内容")
    private String warningContent;

    /** 防御指南 */
    private String preventionGuide;

    /** 受影响道路 */
    private String affectedRoads;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "失效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 状态（0-有效 1-失效 2-取消） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=失效,2=取消")
    private String status;

    /** 关联的影响区域列表（不存数据库） */
    private List<WeatherWarningArea> affectedAreas;

    /** 影响区域的简要描述（用于显示） */
    private String affectedAreasDesc;

    /** 通知统计信息（不存数据库） */
    private Integer totalNotifications;
    private Integer confirmedNotifications;
    private Integer unconfirmedNotifications;
    private Integer timeoutNotifications;

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public String getWarningId() 
    {
        return warningId;
    }

    public void setWarningType(String warningType) 
    {
        this.warningType = warningType;
    }

    public String getWarningType() 
    {
        return warningType;
    }

    public void setWarningLevel(String warningLevel) 
    {
        this.warningLevel = warningLevel;
    }

    public String getWarningLevel() 
    {
        return warningLevel;
    }

    public void setWarningContent(String warningContent) 
    {
        this.warningContent = warningContent;
    }

    public String getWarningContent() 
    {
        return warningContent;
    }

    public void setPreventionGuide(String preventionGuide) 
    {
        this.preventionGuide = preventionGuide;
    }

    public String getPreventionGuide() 
    {
        return preventionGuide;
    }

    public void setAffectedRoads(String affectedRoads) 
    {
        this.affectedRoads = affectedRoads;
    }

    public String getAffectedRoads() 
    {
        return affectedRoads;
    }

    public void setIssueTime(Date issueTime) 
    {
        this.issueTime = issueTime;
    }

    public Date getIssueTime() 
    {
        return issueTime;
    }

    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public List<WeatherWarningArea> getAffectedAreas() 
    {
        return affectedAreas;
    }

    public void setAffectedAreas(List<WeatherWarningArea> affectedAreas) 
    {
        this.affectedAreas = affectedAreas;
    }

    public String getAffectedAreasDesc() 
    {
        return affectedAreasDesc;
    }

    public void setAffectedAreasDesc(String affectedAreasDesc) 
    {
        this.affectedAreasDesc = affectedAreasDesc;
    }

    public Integer getTotalNotifications() 
    {
        return totalNotifications;
    }

    public void setTotalNotifications(Integer totalNotifications) 
    {
        this.totalNotifications = totalNotifications;
    }

    public Integer getConfirmedNotifications() 
    {
        return confirmedNotifications;
    }

    public void setConfirmedNotifications(Integer confirmedNotifications) 
    {
        this.confirmedNotifications = confirmedNotifications;
    }

    public Integer getUnconfirmedNotifications() 
    {
        return unconfirmedNotifications;
    }

    public void setUnconfirmedNotifications(Integer unconfirmedNotifications) 
    {
        this.unconfirmedNotifications = unconfirmedNotifications;
    }

    public Integer getTimeoutNotifications() 
    {
        return timeoutNotifications;
    }

    public void setTimeoutNotifications(Integer timeoutNotifications) 
    {
        this.timeoutNotifications = timeoutNotifications;
    }

    @Override
    public String toString() {
        return "WeatherWarning{" +
                "warningId='" + warningId + '\'' +
                ", warningType='" + warningType + '\'' +
                ", warningLevel='" + warningLevel + '\'' +
                ", warningContent='" + warningContent + '\'' +
                ", preventionGuide='" + preventionGuide + '\'' +
                ", affectedRoads='" + affectedRoads + '\'' +
                ", issueTime=" + issueTime +
                ", expireTime=" + expireTime +
                ", status='" + status + '\'' +
                '}';
    }
}
