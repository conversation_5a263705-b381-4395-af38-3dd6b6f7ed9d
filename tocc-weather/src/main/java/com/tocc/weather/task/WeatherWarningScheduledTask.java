package com.tocc.weather.task;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningNotification;
import com.tocc.weather.service.IWeatherWarningService;
import com.tocc.weather.service.IWeatherWarningNotificationService;
import com.tocc.weather.service.WeatherWarningAlarmService;

/**
 * 气象预警定时任务
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component("weatherWarningTask")
public class WeatherWarningScheduledTask 
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningScheduledTask.class);

    @Autowired
    private IWeatherWarningNotificationService notificationService;

    @Autowired
    private IWeatherWarningService warningService;

    @Autowired
    private WeatherWarningAlarmService weatherAlarmService;

    /**
     * 检查超时通知并创建告警
     * 
     * 使用方法：在系统定时任务中配置
     * 任务名称：检查气象预警超时通知
     * 调用目标：weatherWarningTask.checkTimeoutNotifications
     * cron表达式：0 * * * * ? （每分钟执行一次）
     */
    public void checkTimeoutNotifications() 
    {
        try {
            log.info("开始检查气象预警超时通知...");
            
            // 1. 查询超时的通知记录
            List<WeatherWarningNotification> timeoutNotifications = 
                notificationService.selectTimeoutNotifications();
            
            if (timeoutNotifications.isEmpty()) {
                log.debug("没有发现超时的气象预警通知");
                return;
            }
            
            log.info("发现 {} 条超时的气象预警通知", timeoutNotifications.size());
            
            // 2. 为每个超时通知创建告警
            int successCount = 0;
            int failCount = 0;
            
            for (WeatherWarningNotification notification : timeoutNotifications) {
                try {
                    // 获取预警信息
                    WeatherWarning warning = warningService.selectWeatherWarningByWarningId(notification.getWarningId());
                    
                    if (warning == null) {
                        log.warn("预警信息不存在，跳过处理：{}", notification.getWarningId());
                        failCount++;
                        continue;
                    }
                    
                    // 创建超时告警
                    String alarmId = weatherAlarmService.createTimeoutAlarm(notification, warning);
                    
                    // 更新通知记录，关联告警ID
                    notificationService.updateNotificationTimeout(
                        notification.getWarningId(), 
                        notification.getContactUserId(), 
                        alarmId
                    );
                    
                    successCount++;
                    
                    log.info("处理超时通知成功：{} -> {}", 
                        notification.getWarningId(), 
                        notification.getContactUserName());
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("处理超时通知失败：{} -> {}", 
                        notification.getWarningId(), 
                        notification.getContactUserName(), e);
                }
            }
            
            log.info("气象预警超时通知检查完成，成功：{}，失败：{}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("检查气象预警超时通知时发生异常", e);
        }
    }

    /**
     * 检查即将过期的预警
     * 
     * 使用方法：在系统定时任务中配置
     * 任务名称：检查即将过期的气象预警
     * 调用目标：weatherWarningTask.checkExpiringWarnings
     * cron表达式：0 */10 * * * ? （每10分钟执行一次）
     */
    public void checkExpiringWarnings() 
    {
        try {
            log.info("开始检查即将过期的气象预警...");
            
            // 查询30分钟内即将过期的预警
            List<WeatherWarning> expiringWarnings = warningService.selectExpiringWarnings(30);
            
            if (expiringWarnings.isEmpty()) {
                log.debug("没有发现即将过期的气象预警");
                return;
            }
            
            log.info("发现 {} 条即将过期的气象预警", expiringWarnings.size());
            
            for (WeatherWarning warning : expiringWarnings) {
                try {
                    // TODO: 发送即将过期提醒
                    // 可以给创建者或相关人员发送提醒通知
                    
                    log.info("预警即将过期：{} - {}", 
                        warning.getWarningId(), 
                        warning.getExpireTime());
                    
                } catch (Exception e) {
                    log.error("处理即将过期预警失败：{}", warning.getWarningId(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("检查即将过期预警时发生异常", e);
        }
    }

    /**
     * 自动失效过期预警
     * 
     * 使用方法：在系统定时任务中配置
     * 任务名称：自动失效过期的气象预警
     * 调用目标：weatherWarningTask.autoExpireWarnings
     * cron表达式：0 */5 * * * ? （每5分钟执行一次）
     */
    public void autoExpireWarnings() 
    {
        try {
            log.info("开始自动失效过期的气象预警...");
            
            // 查询已过期但状态仍为有效的预警
            List<WeatherWarning> expiredWarnings = warningService.selectExpiringWarnings(-1);
            
            if (expiredWarnings.isEmpty()) {
                log.debug("没有发现需要自动失效的气象预警");
                return;
            }
            
            log.info("发现 {} 条需要自动失效的气象预警", expiredWarnings.size());
            
            int successCount = 0;
            int failCount = 0;
            
            for (WeatherWarning warning : expiredWarnings) {
                try {
                    // 更新预警状态为失效
                    int result = warningService.updateWeatherWarningStatus(warning.getWarningId(), "1");
                    
                    if (result > 0) {
                        successCount++;
                        log.info("自动失效预警成功：{}", warning.getWarningId());
                    } else {
                        failCount++;
                        log.warn("自动失效预警失败：{}", warning.getWarningId());
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("自动失效预警异常：{}", warning.getWarningId(), e);
                }
            }
            
            log.info("自动失效过期预警完成，成功：{}，失败：{}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("自动失效过期预警时发生异常", e);
        }
    }

    /**
     * 清理历史数据
     * 
     * 使用方法：在系统定时任务中配置
     * 任务名称：清理气象预警历史数据
     * 调用目标：weatherWarningTask.cleanHistoryData
     * cron表达式：0 0 2 * * ? （每天凌晨2点执行）
     */
    public void cleanHistoryData() 
    {
        try {
            log.info("开始清理气象预警历史数据...");
            
            // TODO: 实现历史数据清理逻辑
            // 例如：删除6个月前的已失效预警记录
            
            log.info("气象预警历史数据清理完成");
            
        } catch (Exception e) {
            log.error("清理气象预警历史数据时发生异常", e);
        }
    }

    /**
     * 统计预警数据
     * 
     * 使用方法：在系统定时任务中配置
     * 任务名称：统计气象预警数据
     * 调用目标：weatherWarningTask.statisticsWarningData
     * cron表达式：0 0 1 * * ? （每天凌晨1点执行）
     */
    public void statisticsWarningData() 
    {
        try {
            log.info("开始统计气象预警数据...");
            
            // TODO: 实现预警数据统计逻辑
            // 例如：统计各类型预警数量、确认率、超时率等
            
            log.info("气象预警数据统计完成");
            
        } catch (Exception e) {
            log.error("统计气象预警数据时发生异常", e);
        }
    }
}
