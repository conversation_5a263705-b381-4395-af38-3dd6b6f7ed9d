package com.tocc.weather.constants;

/**
 * 气象预警常量类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningConstants
{
    /** 广西壮族自治区交通运输厅单位ID */
    public static final String GUANGXI_TRANSPORT_DEPT_ID = "100";

    /** 告警类型 */
    public static final String ALARM_TYPE = "3";           // 气象告警
    public static final String ALARM_SUBTYPE = "7";        // 气象告警子类型

    /** 是否已通知 */
    public static final String NOT_NOTIFIED = "0";         // 未通知
    public static final String NOTIFIED = "1";             // 已通知
    /**
     * 预警状态
     */
    public static class WarningStatus 
    {
        /** 有效 */
        public static final String ACTIVE = "0";
        /** 失效 */
        public static final String EXPIRED = "1";
        /** 取消 */
        public static final String CANCELLED = "2";
    }

    /**
     * 预警等级
     */
    public static class WarningLevel 
    {
        /** 蓝色预警 */
        public static final String BLUE = "5";
        /** 黄色预警 */
        public static final String YELLOW = "6";
        /** 橙色预警 */
        public static final String ORANGE = "7";
        /** 红色预警 */
        public static final String RED = "8";
    }

    /**
     * 预警类型
     */
    public static class WarningType 
    {
        /** 暴雨预警 */
        public static final String RAINSTORM = "1";
        /** 台风预警 */
        public static final String TYPHOON = "2";
        /** 雷电预警 */
        public static final String THUNDER = "3";
        /** 大风预警 */
        public static final String WIND = "4";
        /** 冰雹预警 */
        public static final String HAIL = "5";
        /** 高温预警 */
        public static final String HIGH_TEMP = "6";
        /** 寒潮预警 */
        public static final String COLD_WAVE = "7";
        /** 大雾预警 */
        public static final String FOG = "8";
        /** 道路结冰预警 */
        public static final String ICE = "9";
        /** 霜冻预警 */
        public static final String FROST = "10";
    }

    /**
     * 确认状态
     */
    public static class ConfirmStatus 
    {
        /** 未确认 */
        public static final String UNCONFIRMED = "0";
        /** 已确认 */
        public static final String CONFIRMED = "1";
    }

    /**
     * 超时状态
     */
    public static class TimeoutStatus 
    {
        /** 未超时 */
        public static final String NOT_TIMEOUT = "0";
        /** 已超时 */
        public static final String TIMEOUT = "1";
    }

    /**
     * 通知方式
     */
    public static class NotificationMethod 
    {
        /** 系统通知 */
        public static final String SYSTEM = "SYSTEM";
        /** 短信通知 */
        public static final String SMS = "SMS";
        /** 邮件通知 */
        public static final String EMAIL = "EMAIL";
        /** 电话通知 */
        public static final String PHONE = "PHONE";
    }

    /**
     * 告警类型
     */
    public static class AlarmType 
    {
        /** 气象告警 */
        public static final int WEATHER = 3;
        /** 气象告警子类型 */
        public static final int WEATHER_SUBTYPE = 7;
    }

    /**
     * 告警子类型
     */
    public static class AlarmSubtype 
    {
        /** 超时告警 */
        public static final String TIMEOUT = "TIMEOUT";
        /** 升级告警 */
        public static final String UPGRADE = "UPGRADE";
        /** 发送失败告警 */
        public static final String SEND_FAILED = "SEND_FAILED";
        /** 取消异常告警 */
        public static final String CANCEL_ERROR = "CANCEL_ERROR";
        /** 信息更新告警 */
        public static final String UPDATE = "UPDATE";
    }

    /**
     * 字典类型
     */
    public static class DictType 
    {
        /** 预警类型字典 */
        public static final String WEATHER_WARNING_TYPE = "weather_warning_type";
        /** 预警等级字典 */
        public static final String ALARM_LEVEL = "alarm_level";
        /** 气象告警类型字典 */
        public static final String WEATHER_ALARM_TYPE = "weather_alarm_type";
    }

    /**
     * 超时时间（分钟）
     */
    public static class TimeoutMinutes 
    {
        /** 红色预警超时时间 */
        public static final int RED_WARNING = 15;
        /** 橙色预警超时时间 */
        public static final int ORANGE_WARNING = 30;
        /** 黄色预警超时时间 */
        public static final int YELLOW_WARNING = 60;
        /** 蓝色预警超时时间 */
        public static final int BLUE_WARNING = 120;
        /** 默认超时时间 */
        public static final int DEFAULT = 30;
    }

    /**
     * 缓存键前缀
     */
    public static class CacheKey 
    {
        /** 预警缓存前缀 */
        public static final String WARNING_PREFIX = "weather:warning:";
        /** 通知缓存前缀 */
        public static final String NOTIFICATION_PREFIX = "weather:notification:";
        /** 统计缓存前缀 */
        public static final String STATS_PREFIX = "weather:stats:";
    }

    /**
     * 消息模板
     */
    public static class MessageTemplate 
    {
        /** 短信通知模板 */
        public static final String SMS_TEMPLATE = "【气象预警】%s%s，发布时间：%s。请及时确认并采取相应措施。";
        /** 邮件主题模板 */
        public static final String EMAIL_SUBJECT_TEMPLATE = "气象预警通知 - %s%s";
        /** 系统通知标题模板 */
        public static final String SYSTEM_TITLE_TEMPLATE = "气象预警通知";
    }

    /**
     * 业务规则
     */
    public static class BusinessRule 
    {
        /** 最大影响区域数量 */
        public static final int MAX_AFFECTED_AREAS = 50;
        /** 最大通知对象数量 */
        public static final int MAX_NOTIFY_TARGETS = 100;
        /** 预警内容最大长度 */
        public static final int MAX_CONTENT_LENGTH = 2000;
        /** 防御指南最大长度 */
        public static final int MAX_GUIDE_LENGTH = 1000;
        /** 受影响道路最大长度 */
        public static final int MAX_ROADS_LENGTH = 500;
    }

    /**
     * 权限标识
     */
    public static class Permission 
    {
        /** 预警列表权限 */
        public static final String WARNING_LIST = "weather:warning:list";
        /** 预警查询权限 */
        public static final String WARNING_QUERY = "weather:warning:query";
        /** 预警新增权限 */
        public static final String WARNING_ADD = "weather:warning:add";
        /** 预警编辑权限 */
        public static final String WARNING_EDIT = "weather:warning:edit";
        /** 预警删除权限 */
        public static final String WARNING_REMOVE = "weather:warning:remove";
        /** 预警导出权限 */
        public static final String WARNING_EXPORT = "weather:warning:export";
        /** 通知列表权限 */
        public static final String NOTIFICATION_LIST = "weather:notification:list";
        /** 通知确认权限 */
        public static final String NOTIFICATION_CONFIRM = "weather:notification:confirm";
    }

    /**
     * 日志操作类型
     */
    public static class LogOperation 
    {
        /** 创建预警 */
        public static final String CREATE_WARNING = "CREATE_WARNING";
        /** 更新预警 */
        public static final String UPDATE_WARNING = "UPDATE_WARNING";
        /** 取消预警 */
        public static final String CANCEL_WARNING = "CANCEL_WARNING";
        /** 确认通知 */
        public static final String CONFIRM_NOTIFICATION = "CONFIRM_NOTIFICATION";
        /** 催办通知 */
        public static final String REMIND_NOTIFICATION = "REMIND_NOTIFICATION";
        /** 发送通知 */
        public static final String SEND_NOTIFICATION = "SEND_NOTIFICATION";
        /** 创建告警 */
        public static final String CREATE_ALARM = "CREATE_ALARM";
    }

    /**
     * 错误码
     */
    public static class ErrorCode 
    {
        /** 预警不存在 */
        public static final String WARNING_NOT_FOUND = "WARNING_NOT_FOUND";
        /** 预警已过期 */
        public static final String WARNING_EXPIRED = "WARNING_EXPIRED";
        /** 无效的预警状态 */
        public static final String INVALID_WARNING_STATUS = "INVALID_WARNING_STATUS";
        /** 通知已确认 */
        public static final String NOTIFICATION_ALREADY_CONFIRMED = "NOTIFICATION_ALREADY_CONFIRMED";
        /** 通知已超时 */
        public static final String NOTIFICATION_TIMEOUT = "NOTIFICATION_TIMEOUT";
        /** 通知发送失败 */
        public static final String NOTIFICATION_SEND_FAILED = "NOTIFICATION_SEND_FAILED";
        /** 告警创建失败 */
        public static final String ALARM_CREATE_FAILED = "ALARM_CREATE_FAILED";
        /** 参数验证错误 */
        public static final String PARAMETER_VALIDATION_ERROR = "PARAMETER_VALIDATION_ERROR";
        /** 业务规则违反 */
        public static final String BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION";
    }
}
