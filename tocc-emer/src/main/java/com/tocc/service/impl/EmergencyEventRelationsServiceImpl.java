package com.tocc.emergency.service.impl;

import java.util.List;

import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.emergency.mapper.EmergencyEventRelationsMapper;
import com.tocc.domain.entity.EmergencyEventRelations;
import com.tocc.emergency.service.IEmergencyEventRelationsService;

/**
 * 应急事件关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class EmergencyEventRelationsServiceImpl implements IEmergencyEventRelationsService 
{
    @Autowired
    private EmergencyEventRelationsMapper emergencyEventRelationsMapper;

    /**
     * 查询应急事件关联
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 应急事件关联
     */
    @Override
    public EmergencyEventRelations selectEmergencyEventRelationsByEventRelationsId(String eventRelationsId)
    {
        return emergencyEventRelationsMapper.selectEmergencyEventRelationsByEventRelationsId(eventRelationsId);
    }

    /**
     * 查询应急事件关联列表
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 应急事件关联
     */
    @Override
    public List<EmergencyEventRelations> selectEmergencyEventRelationsList(EmergencyEventRelations emergencyEventRelations)
    {
        return emergencyEventRelationsMapper.selectEmergencyEventRelationsList(emergencyEventRelations);
    }

    /**
     * 新增应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    @Override
    public int insertEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        emergencyEventRelations.setCreator(loginUser.getUsername());
        emergencyEventRelations.setUpdater(loginUser.getUsername());
        emergencyEventRelations.setEventRelationsId(IdUtils.fastSimpleUUID());
        return emergencyEventRelationsMapper.insertEmergencyEventRelations(emergencyEventRelations);
    }

    /**
     * 修改应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    @Override
    public int updateEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations)
    {
        emergencyEventRelations.setUpdateTime(DateUtils.getNowDate());
        return emergencyEventRelationsMapper.updateEmergencyEventRelations(emergencyEventRelations);
    }

    /**
     * 批量删除应急事件关联
     * 
     * @param eventRelationsIds 需要删除的应急事件关联主键
     * @return 结果
     */
    @Override
    public int deleteEmergencyEventRelationsByEventRelationsIds(String[] eventRelationsIds)
    {
        return emergencyEventRelationsMapper.deleteEmergencyEventRelationsByEventRelationsIds(eventRelationsIds);
    }

    /**
     * 删除应急事件关联信息
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 结果
     */
    @Override
    public int deleteEmergencyEventRelationsByEventRelationsId(String eventRelationsId)
    {
        return emergencyEventRelationsMapper.deleteEmergencyEventRelationsByEventRelationsId(eventRelationsId);
    }
}
