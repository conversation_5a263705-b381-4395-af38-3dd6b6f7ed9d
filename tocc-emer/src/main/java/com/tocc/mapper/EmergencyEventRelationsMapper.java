package com.tocc.emergency.mapper;

import java.util.List;
import com.tocc.domain.entity.EmergencyEventRelations;

/**
 * 应急事件关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface EmergencyEventRelationsMapper 
{
    /**
     * 查询应急事件关联
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 应急事件关联
     */
    public EmergencyEventRelations selectEmergencyEventRelationsByEventRelationsId(String eventRelationsId);

    /**
     * 查询应急事件关联列表
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 应急事件关联集合
     */
    public List<EmergencyEventRelations> selectEmergencyEventRelationsList(EmergencyEventRelations emergencyEventRelations);

    /**
     * 新增应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    public int insertEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations);

    /**
     * 修改应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    public int updateEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations);

    /**
     * 删除应急事件关联
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 结果
     */
    public int deleteEmergencyEventRelationsByEventRelationsId(String eventRelationsId);

    /**
     * 批量删除应急事件关联
     * 
     * @param eventRelationsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmergencyEventRelationsByEventRelationsIds(String[] eventRelationsIds);
}
