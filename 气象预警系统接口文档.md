# 气象预警系统接口文档

## 概述

气象预警系统提供预警信息的创建、查询、通知确认等功能。系统支持多种预警类型和等级，并能自动发送通知给相关人员。

### 基础信息

- **基础URL**: `/weather`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 预警类型字典

| 值 | 名称 | 描述 |
|---|---|---|
| 1 | 暴雨预警 | 暴雨天气预警 |
| 2 | 台风预警 | 台风天气预警 |
| 3 | 雷电预警 | 雷电天气预警 |
| 4 | 大风预警 | 大风天气预警 |
| 5 | 冰雹预警 | 冰雹天气预警 |
| 6 | 高温预警 | 高温天气预警 |
| 7 | 寒潮预警 | 寒潮天气预警 |
| 8 | 大雾预警 | 大雾天气预警 |
| 9 | 道路结冰预警 | 道路结冰预警 |
| 10 | 霜冻预警 | 霜冻天气预警 |

### 预警等级字典

| 值 | 名称 | 颜色 | 超时时间 |
|---|---|---|---|
| 5 | 蓝色预警 | #0066FF | 120分钟 |
| 6 | 黄色预警 | #FFCC00 | 60分钟 |
| 7 | 橙色预警 | #FF8C00 | 30分钟 |
| 8 | 红色预警 | #FF0000 | 15分钟 |

### 预警状态字典

| 值 | 名称 | 描述 |
|---|---|---|
| 0 | 有效 | 预警有效 |
| 1 | 失效 | 预警失效 |
| 2 | 取消 | 预警取消 |

### 确认状态字典

| 值 | 名称 | 描述 |
|---|---|---|
| 0 | 未确认 | 尚未确认 |
| 1 | 已确认 | 已经确认 |

---

## 1. 预警管理接口

### 1.1 查询预警列表

**接口地址**: `GET /weather/warning/list`

**接口描述**: 分页查询气象预警信息列表，支持多种条件筛选

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningType | String | 否 | 预警类型 | 1 |
| warningLevel | String | 否 | 预警等级 | 8 |
| status | String | 否 | 状态 | 0 |
| issueTimeStart | String | 否 | 发布时间开始 | 2024-01-01 00:00:00 |
| issueTimeEnd | String | 否 | 发布时间结束 | 2024-12-31 23:59:59 |
| regionId | String | 否 | 影响区域ID | 450100 |
| createBy | String | 否 | 创建人 | admin |
| contentKeyword | String | 否 | 内容关键字 | 暴雨 |
| pageNum | Integer | 否 | 页码 | 1 |
| pageSize | Integer | 否 | 每页数量 | 10 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "warningId": "uuid-123",
      "warningType": "1",
      "warningLevel": "8",
      "warningContent": "预计未来3小时内，南宁市区及各县区将出现100毫米以上强降雨...",
      "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作...",
      "affectedRoads": "G80广昆高速玉林段、S21玉铁高速全线...",
      "issueTime": "2024-05-20 07:30:00",
      "expireTime": "2024-05-20 18:00:00",
      "status": "0",
      "createBy": "admin",
      "createTime": "2024-05-20 07:25:00",
      "affectedAreasDesc": "南宁市、青秀区、江南区",
      "totalNotifications": 5,
      "confirmedNotifications": 3,
      "unconfirmedNotifications": 2,
      "timeoutNotifications": 0
    }
  ],
  "total": 1
}
```

### 1.2 获取预警详情

**接口地址**: `GET /weather/warning/{warningId}`

**接口描述**: 根据预警ID获取详细信息，包括影响区域和通知统计

**权限要求**: `weather:warning:query`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "warningId": "uuid-123",
    "warningType": "1",
    "warningTypeLabel": "暴雨预警",
    "warningLevel": "8",
    "warningLevelLabel": "红色预警",
    "warningContent": "预计未来3小时内，南宁市区及各县区将出现100毫米以上强降雨...",
    "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作...",
    "affectedRoads": "G80广昆高速玉林段、S21玉铁高速全线...",
    "issueTime": "2024-05-20 07:30:00",
    "expireTime": "2024-05-20 18:00:00",
    "status": "0",
    "statusLabel": "有效",
    "createTime": "2024-05-20 07:25:00",
    "createBy": "admin",
    "affectedAreas": [
      {
        "warningId": "uuid-123",
        "regionId": "450100",
        "regionName": "南宁市",
        "regionFullPath": "广西壮族自治区/南宁市",
        "createTime": "2024-05-20 07:25:00"
      }
    ],
    "affectedAreasDesc": "南宁市、青秀区、江南区",
    "totalNotifications": 5,
    "confirmedNotifications": 3,
    "unconfirmedNotifications": 2,
    "timeoutNotifications": 0
  }
}
```

### 1.3 创建预警

**接口地址**: `POST /weather/warning`

**接口描述**: 创建新的气象预警并发送通知给指定人员

**权限要求**: `weather:warning:add`

**请求参数**:

```json
{
  "warningType": "1",
  "warningLevel": "8",
  "warningContent": "预计未来3小时内，南宁市区及各县区将出现100毫米以上强降雨，局部地区可能超过150毫米，并伴有雷电、短时大风等强对流天气。请注意防范山洪、地质灾害、城市内涝等次生灾害。",
  "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作\n切断有危险的室外电源，暂停户外作业",
  "affectedRoads": "G80广昆高速玉林段\nS21玉铁高速全线",
  "issueTime": "2024-05-20 07:30:00",
  "expireTime": "2024-05-20 18:00:00",
  "affectedAreas": [
    {
      "regionId": "450100",
      "regionName": "南宁市"
    },
    {
      "regionId": "450103",
      "regionName": "青秀区"
    }
  ],
  "notifyTargets": [
    {
      "contactUserId": 1,
      "contactUserName": "张三",
      "contactUnitName": "应急管理局",
      "contactDeptName": "综合科",
      "contactPostName": "科长",
      "contactPhone": "13800138000",
      "contactEmail": "<EMAIL>"
    }
  ]
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 描述 |
|---|---|---|---|
| warningType | String | 是 | 预警类型 |
| warningLevel | String | 是 | 预警等级 |
| warningContent | String | 是 | 预警内容 |
| preventionGuide | String | 否 | 防御指南 |
| affectedRoads | String | 否 | 受影响道路 |
| issueTime | String | 是 | 发布时间 |
| expireTime | String | 否 | 失效时间 |
| affectedAreas | Array | 是 | 影响区域列表 |
| notifyTargets | Array | 是 | 通知对象列表 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "预警创建成功",
  "data": "uuid-123"
}
```

### 1.4 更新预警状态

**接口地址**: `PUT /weather/warning/{warningId}/status`

**接口描述**: 更新预警状态（有效/失效/取消）

**权限要求**: `weather:warning:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |
| status | String | 是 | 新状态 | 1 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 1.5 取消预警

**接口地址**: `PUT /weather/warning/{warningId}/cancel`

**接口描述**: 取消预警并记录取消原因

**权限要求**: `weather:warning:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |
| reason | String | 是 | 取消原因 | 天气好转，预警解除 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 1.6 查询有效预警

**接口地址**: `GET /weather/warning/active`

**接口描述**: 查询当前有效的预警列表

**权限要求**: `weather:warning:list`

**请求参数**: 无

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "warningType": "1",
      "warningLevel": "8",
      "warningContent": "预计未来3小时内...",
      "issueTime": "2024-05-20 07:30:00",
      "expireTime": "2024-05-20 18:00:00",
      "status": "0",
      "affectedAreasDesc": "南宁市、青秀区"
    }
  ]
}
```

### 1.7 查询即将过期预警

**接口地址**: `GET /weather/warning/expiring`

**接口描述**: 查询即将过期的预警列表

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| minutes | Integer | 否 | 提前多少分钟 | 60 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "warningType": "1",
      "warningLevel": "8",
      "warningContent": "预计未来3小时内...",
      "issueTime": "2024-05-20 07:30:00",
      "expireTime": "2024-05-20 18:00:00",
      "status": "0",
      "affectedAreasDesc": "南宁市、青秀区"
    }
  ]
}
```

---

## 2. 通知管理接口

### 2.1 查询通知进展

**接口地址**: `GET /weather/notification/progress/{warningId}`

**接口描述**: 查询指定预警的通知进展情况，包括确认状态和剩余时间

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "contactUserId": 1,
      "contactUnitName": "应急管理局",
      "contactDeptName": "综合科",
      "contactPostName": "科长",
      "contactUserName": "张三",
      "contactPhone": "13800138000",
      "notificationTime": "2024-05-20 07:35:00",
      "confirmStatus": "1",
      "confirmStatusLabel": "已确认",
      "confirmTime": "2024-05-20 07:40:00",
      "confirmUserName": "张三",
      "timeoutMinutes": 15,
      "isTimeout": "0",
      "remainingMinutes": 0
    },
    {
      "warningId": "uuid-123",
      "contactUserId": 2,
      "contactUnitName": "交通运输局",
      "contactDeptName": "安全监管科",
      "contactPostName": "副科长",
      "contactUserName": "李四",
      "contactPhone": "13800138001",
      "notificationTime": "2024-05-20 07:35:00",
      "confirmStatus": "0",
      "confirmStatusLabel": "未确认",
      "confirmTime": null,
      "confirmUserName": null,
      "timeoutMinutes": 15,
      "isTimeout": "0",
      "remainingMinutes": 8
    }
  ]
}
```

### 2.2 确认预警通知

**接口地址**: `POST /weather/notification/confirm/{warningId}/{contactUserId}`

**接口描述**: 确认收到的预警通知

**权限要求**: `weather:notification:confirm`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |
| contactUserId | Long | 是 | 联系人用户ID | 1 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "确认成功"
}
```

**错误返回示例**:

```json
{
  "code": 500,
  "msg": "确认失败，请检查通知是否存在或已确认"
}
```

### 2.3 催办未确认通知

**接口地址**: `POST /weather/notification/remind/{warningId}`

**接口描述**: 对未确认的通知进行催办，重新发送通知

**权限要求**: `weather:warning:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "催办通知已发送",
  "data": 2
}
```

### 2.4 查询我的预警通知

**接口地址**: `GET /weather/notification/my`

**接口描述**: 查询当前登录用户收到的预警通知列表

**权限要求**: `weather:notification:list`

**请求参数**: 无（从当前登录用户获取）

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "contactUserId": 1,
      "contactUnitName": "应急管理局",
      "contactDeptName": "综合科",
      "contactPostName": "科长",
      "contactUserName": "张三",
      "contactPhone": "13800138000",
      "contactEmail": "<EMAIL>",
      "notificationTime": "2024-05-20 07:35:00",
      "confirmStatus": "0",
      "confirmTime": null,
      "timeoutMinutes": 15,
      "isTimeout": "0",
      "createTime": "2024-05-20 07:35:00",
      "warning": {
        "warningId": "uuid-123",
        "warningType": "1",
        "warningLevel": "8",
        "warningContent": "预计未来3小时内...",
        "issueTime": "2024-05-20 07:30:00",
        "status": "0"
      }
    }
  ]
}
```

### 2.5 查询指定预警的通知列表

**接口地址**: `GET /weather/notification/list/{warningId}`

**接口描述**: 查询指定预警的所有通知记录

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "contactUserId": 1,
      "contactUnitName": "应急管理局",
      "contactDeptName": "综合科",
      "contactPostName": "科长",
      "contactUserName": "张三",
      "contactPhone": "13800138000",
      "contactEmail": "<EMAIL>",
      "notificationTime": "2024-05-20 07:35:00",
      "confirmStatus": "1",
      "confirmTime": "2024-05-20 07:40:00",
      "confirmUserId": 1,
      "confirmUserName": "张三",
      "timeoutMinutes": 15,
      "isTimeout": "0",
      "createTime": "2024-05-20 07:35:00"
    }
  ]
}
```

### 2.6 查询未确认通知

**接口地址**: `GET /weather/notification/unconfirmed/{warningId}`

**接口描述**: 查询指定预警的未确认通知列表

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID | uuid-123 |

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "contactUserId": 2,
      "contactUnitName": "交通运输局",
      "contactUserName": "李四",
      "contactPhone": "13800138001",
      "notificationTime": "2024-05-20 07:35:00",
      "confirmStatus": "0",
      "timeoutMinutes": 15,
      "isTimeout": "0"
    }
  ]
}
```

### 2.7 查询超时通知

**接口地址**: `GET /weather/notification/timeout`

**接口描述**: 查询所有超时的通知记录

**权限要求**: `weather:warning:list`

**请求参数**: 无

**返回示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "warningId": "uuid-123",
      "contactUserId": 3,
      "contactUnitName": "水利局",
      "contactUserName": "王五",
      "contactPhone": "13800138002",
      "notificationTime": "2024-05-20 07:35:00",
      "confirmStatus": "0",
      "timeoutMinutes": 15,
      "isTimeout": "1",
      "alarmId": "alarm-uuid-456"
    }
  ]
}
```

---

## 3. 错误码说明

### 3.1 通用错误码

| 错误码 | HTTP状态码 | 描述 |
|---|---|---|
| 200 | 200 | 操作成功 |
| 401 | 401 | 未授权，请先登录 |
| 403 | 403 | 权限不足 |
| 404 | 404 | 资源不存在 |
| 500 | 500 | 服务器内部错误 |

### 3.2 业务错误码

| 错误码 | 描述 |
|---|---|
| WARNING_NOT_FOUND | 预警不存在 |
| WARNING_EXPIRED | 预警已过期 |
| INVALID_WARNING_STATUS | 无效的预警状态 |
| NOTIFICATION_ALREADY_CONFIRMED | 通知已确认 |
| NOTIFICATION_TIMEOUT | 通知已超时 |
| NOTIFICATION_SEND_FAILED | 通知发送失败 |
| ALARM_CREATE_FAILED | 告警创建失败 |
| PARAMETER_VALIDATION_ERROR | 参数验证错误 |
| BUSINESS_RULE_VIOLATION | 业务规则违反 |

---

## 4. 定时任务说明

### 4.1 超时检查任务

**任务名称**: 检查气象预警超时通知
**调用目标**: `weatherWarningTask.checkTimeoutNotifications`
**执行频率**: 每分钟执行一次
**cron表达式**: `0 * * * * ?`
**功能**: 检查超时的通知记录并创建告警

### 4.2 即将过期检查任务

**任务名称**: 检查即将过期的气象预警
**调用目标**: `weatherWarningTask.checkExpiringWarnings`
**执行频率**: 每10分钟执行一次
**cron表达式**: `0 */10 * * * ?`
**功能**: 检查即将过期的预警并发送提醒

### 4.3 自动失效任务

**任务名称**: 自动失效过期的气象预警
**调用目标**: `weatherWarningTask.autoExpireWarnings`
**执行频率**: 每5分钟执行一次
**cron表达式**: `0 */5 * * * ?`
**功能**: 自动将过期的预警状态更新为失效

### 4.4 数据清理任务

**任务名称**: 清理气象预警历史数据
**调用目标**: `weatherWarningTask.cleanHistoryData`
**执行频率**: 每天凌晨2点执行
**cron表达式**: `0 0 2 * * ?`
**功能**: 清理6个月前的历史数据

---

## 5. 通知机制说明

### 5.1 通知方式

1. **系统内通知**: 在系统内显示通知消息
2. **短信通知**: 发送短信到联系人手机
3. **邮件通知**: 发送邮件到联系人邮箱

### 5.2 超时机制

根据预警等级自动设置超时时间：
- 红色预警：15分钟
- 橙色预警：30分钟
- 黄色预警：60分钟
- 蓝色预警：120分钟

### 5.3 告警机制

当通知超时时，系统会自动创建告警：
- **告警类型**: 3（气象告警）
- **告警子类型**: 7（气象告警子类型）
- **具体类型**: TIMEOUT（超时告警）

---

## 6. 使用示例

### 6.1 创建暴雨红色预警

```bash
curl -X POST "http://localhost:8080/weather/warning" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "warningType": "1",
    "warningLevel": "8",
    "warningContent": "预计未来3小时内，南宁市区将出现100毫米以上强降雨",
    "issueTime": "2024-05-20 07:30:00",
    "expireTime": "2024-05-20 18:00:00",
    "affectedAreas": [
      {"regionId": "450100", "regionName": "南宁市"}
    ],
    "notifyTargets": [
      {
        "contactUserId": 1,
        "contactUserName": "张三",
        "contactUnitName": "应急管理局",
        "contactPhone": "13800138000"
      }
    ]
  }'
```

### 6.2 确认预警通知

```bash
curl -X POST "http://localhost:8080/weather/notification/confirm/uuid-123/1" \
  -H "Authorization: Bearer your-jwt-token"
```

### 6.3 查询我的通知

```bash
curl -X GET "http://localhost:8080/weather/notification/my" \
  -H "Authorization: Bearer your-jwt-token"
```

---

## 7. 注意事项

1. **权限控制**: 所有接口都需要相应的权限才能访问
2. **数据验证**: 创建预警时会验证必填字段和数据格式
3. **并发控制**: 同一通知不能重复确认
4. **超时处理**: 系统会自动处理超时通知并创建告警
5. **日志记录**: 所有操作都会记录详细的日志信息

---

## 8. 联系方式

如有问题请联系开发团队。
