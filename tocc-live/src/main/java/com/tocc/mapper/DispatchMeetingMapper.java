package com.tocc.mapper;

import java.util.List;

import com.tocc.domain.DispatchMeeting;

/**
 * 现场指挥协调会议Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface DispatchMeetingMapper {

    /**
     * 查询现场指挥协调会议
     *
     * @param id 现场指挥协调会议主键
     * @return 现场指挥协调会议
     */
    public DispatchMeeting selectDispatchMeetingById(Long id);

    /**
     * 查询现场指挥协调会议列表
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 现场指挥协调会议集合
     */
    public List<DispatchMeeting> selectDispatchMeetingList(DispatchMeeting dispatchMeeting);

    /**
     * 新增现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    public int insertDispatchMeeting(DispatchMeeting dispatchMeeting);

    /**
     * 修改现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    public int updateDispatchMeeting(DispatchMeeting dispatchMeeting);

    /**
     * 删除现场指挥协调会议
     *
     * @param id 现场指挥协调会议主键
     * @return 结果
     */
    public int deleteDispatchMeetingById(Long id);

    /**
     * 批量删除现场指挥协调会议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDispatchMeetingByIds(Long[] ids);


    /**
     * 查询现场指挥协调会议
     *
     * @param code 会议码
     * @return 现场指挥协调会议
     */
    DispatchMeeting getMeetingInfoByCode(String code);

    /**
     * 查询现场指挥协调会议
     *
     * @param channelName 频道
     * @return 现场指挥协调会议
     */
    DispatchMeeting getMeetingInfoByChannelName(String channelName);

}
