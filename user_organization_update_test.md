# 用户组织归属功能测试指南

## 修改完成情况

我们已经成功完成了用户表组织归属功能的修改，主要包括：

### ✅ 已完成的修改

#### 1. SysUser实体类更新
- ✅ 添加了 `orgId` 字段（Long类型）
- ✅ 添加了相应的 getter/setter 方法
- ✅ 添加了业务判断方法：
  - `isDirectlyBelongToUnit()` - 判断用户是否直接属于单位
  - `isBelongToDepartment()` - 判断用户是否属于部门
- ✅ 更新了 `toString()` 方法

#### 2. Mapper映射文件更新
- ✅ 在 `SysUserMapper.xml` 中添加了 `org_id` 字段映射
- ✅ 更新了查询语句以包含 `org_id` 字段
- ✅ 更新了插入和修改语句以支持 `org_id` 字段
- ✅ 添加了按 `orgId` 查询的条件支持

#### 3. 业务逻辑层更新
- ✅ 在 `SysUserServiceImpl` 中添加了 `setUserOrganization()` 方法
- ✅ 添加了 `findTopUnitId()` 方法来查找顶级单位
- ✅ 添加了按单位和部门查询用户的方法
- ✅ 在 `insertUser()` 和 `updateUser()` 方法中集成了组织归属逻辑

#### 4. 组织架构查询优化
- ✅ 更新了 `OrganizationTreeMapper.xml` 以支持新的用户组织关系

## 字段含义说明

### 修改后的字段定义
- **`dept_id`**：用户直接所属的组织ID（可能是单位ID或部门ID）
- **`org_id`**：用户所属的顶级单位ID（始终指向 org_type='1' 的记录）

### 业务逻辑
1. **用户直接属于单位**：`dept_id = org_id`
2. **用户属于部门**：`dept_id ≠ org_id`，其中 `dept_id` 指向部门，`org_id` 指向该部门所属的单位

## 测试步骤

### 1. 数据验证
```sql
-- 检查现有用户数据的组织归属
SELECT 
    u.user_id,
    u.user_name,
    u.dept_id,
    u.org_id,
    d1.dept_name as dept_name,
    d1.org_type as dept_org_type,
    d2.dept_name as org_name,
    d2.org_type as org_org_type,
    CASE 
        WHEN u.dept_id = u.org_id THEN '直接属于单位'
        ELSE '属于部门'
    END as belong_type
FROM sys_user u 
LEFT JOIN sys_dept d1 ON u.dept_id = d1.dept_id 
LEFT JOIN sys_dept d2 ON u.org_id = d2.dept_id 
WHERE u.del_flag = '0'
ORDER BY u.org_id, u.dept_id;
```

### 2. 新增用户测试

#### 测试场景1：用户直接属于单位
```java
// 创建用户，选择单位
SysUser user = new SysUser();
user.setUserName("test_unit_user");
user.setNickName("单位用户");
user.setDeptId(100L); // 假设100是单位ID
// 系统应自动设置 org_id = 100
```

#### 测试场景2：用户属于部门
```java
// 创建用户，选择部门
SysUser user = new SysUser();
user.setUserName("test_dept_user");
user.setNickName("部门用户");
user.setDeptId(101L); // 假设101是部门ID，其父单位是100
// 系统应自动设置 org_id = 100
```

### 3. 修改用户测试

#### 测试场景1：从单位调到部门
```java
// 将用户从单位调到部门
SysUser user = userService.selectUserById(userId);
user.setDeptId(101L); // 调到部门
userService.updateUser(user);
// 验证：dept_id = 101, org_id = 100
```

#### 测试场景2：从部门调到单位
```java
// 将用户从部门调到单位
SysUser user = userService.selectUserById(userId);
user.setDeptId(100L); // 调到单位
userService.updateUser(user);
// 验证：dept_id = 100, org_id = 100
```

### 4. 查询功能测试

#### 按单位查询用户
```java
List<SysUser> unitUsers = userService.selectUsersByUnitId(100L);
// 应返回所有 org_id = 100 的用户
```

#### 按部门查询用户
```java
List<SysUser> deptUsers = userService.selectUsersByDeptId(101L);
// 应返回所有 dept_id = 101 的用户
```

### 5. 组织架构树测试

#### 测试组织架构显示
```java
// 测试组织架构树是否正确显示用户归属
List<OrganizationTreeVO> tree = organizationTreeService.getOrganizationTree(null);
// 验证用户节点的 parent_id 是否正确设置
```

## 预期结果

### 1. 数据一致性
- 所有用户的 `org_id` 都应指向单位类型的组织（`org_type='1'`）
- 用户的 `dept_id` 可以指向单位或部门

### 2. 业务逻辑正确性
- 新增用户时，系统自动计算并设置正确的 `org_id`
- 修改用户部门时，系统自动更新 `org_id`
- 查询功能按预期工作

### 3. 组织架构树正确性
- 用户节点正确挂在其直接所属的组织下
- 树形结构层级关系正确

## 可能的问题和解决方案

### 1. 数据迁移问题
**问题**：现有用户的 `org_id` 可能为空或不正确
**解决**：运行数据修复脚本
```sql
UPDATE sys_user u 
SET u.org_id = (
    CASE 
        WHEN (SELECT d.org_type FROM sys_dept d WHERE d.dept_id = u.dept_id) = '1' 
        THEN u.dept_id
        ELSE (
            SELECT CAST(SUBSTRING_INDEX(d.ancestors, ',', 1) AS UNSIGNED)
            FROM sys_dept d 
            WHERE d.dept_id = u.dept_id AND d.org_type = '0'
        )
    END
)
WHERE u.dept_id IS NOT NULL AND u.del_flag = '0';
```

### 2. 性能问题
**问题**：查询时可能需要多次关联 sys_dept 表
**解决**：确保相关字段有索引，考虑添加冗余字段

### 3. 前端适配
**问题**：前端可能需要适配新的字段结构
**解决**：更新前端代码以支持 `orgId` 字段

## 后续工作

1. **前端界面更新**：在用户管理界面中显示用户的单位和部门信息
2. **权限控制优化**：基于新的组织归属关系优化数据权限控制
3. **报表功能**：利用新的字段结构优化组织相关的报表查询
4. **API文档更新**：更新相关API文档以反映字段变化
