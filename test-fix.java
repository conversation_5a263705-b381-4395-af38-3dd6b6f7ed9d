import java.time.LocalDateTime;
import java.util.Date;

/**
 * Simple test to verify the EmPrePlanVO class structure matches our MyBatis mapping
 */
public class TestFix {
    
    public static void main(String[] args) {
        System.out.println("Testing EmPrePlanVO class structure...");
        
        try {
            // Test that EmPrePlanVO class exists and has the required fields
            Class<?> voClass = Class.forName("com.tocc.em.vo.EmPrePlanVO");
            System.out.println("✓ EmPrePlanVO class found");
            
            // Check for key fields that are used in the timeout check
            checkField(voClass, "id", String.class);
            checkField(voClass, "planName", String.class);
            checkField(voClass, "lastCheckTime", Date.class);
            checkField(voClass, "creator", String.class);
            
            System.out.println("✓ All required fields found in EmPrePlanVO");
            System.out.println("✓ MyBatis mapping should work correctly now");
            
            // Test that EmPrePlan entity class also exists
            Class<?> entityClass = Class.forName("com.tocc.em.domain.EmPrePlan");
            System.out.println("✓ EmPrePlan entity class found");
            
            System.out.println("\n=== Fix Summary ===");
            System.out.println("The ClassCastException was caused by:");
            System.out.println("1. MyBatis mapper was using EmPrePlanResult (maps to EmPrePlan entity)");
            System.out.println("2. But the service expected List<EmPrePlanVO>");
            System.out.println("3. Fixed by creating EmPrePlanVOResult mapping and updating selectTimeoutPlans query");
            System.out.println("\nThe fix should resolve the ClassCastException in InfoUpdateTimeoutCheckTask.checkPrePlanTimeout()");
            
        } catch (ClassNotFoundException e) {
            System.err.println("✗ Class not found: " + e.getMessage());
            System.err.println("Make sure the application is compiled properly");
        } catch (Exception e) {
            System.err.println("✗ Error: " + e.getMessage());
        }
    }
    
    private static void checkField(Class<?> clazz, String fieldName, Class<?> expectedType) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            if (field.getType().equals(expectedType)) {
                System.out.println("  ✓ Field '" + fieldName + "' found with correct type: " + expectedType.getSimpleName());
            } else {
                System.out.println("  ⚠ Field '" + fieldName + "' found but with different type: " + field.getType().getSimpleName() + " (expected: " + expectedType.getSimpleName() + ")");
            }
        } catch (NoSuchFieldException e) {
            System.out.println("  ✗ Field '" + fieldName + "' not found");
        }
    }
}
