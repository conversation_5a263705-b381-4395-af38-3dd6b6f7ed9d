# 前端接口文档 - 组织架构和用户管理更新

## 概述

本文档列出了在实现单位-部门区分功能后，所有有变更的前端接口。主要涉及用户管理和组织架构相关的接口。

## 🔄 变更说明

### 核心变更
1. **用户实体新增字段**：`orgId`（所属单位ID）
2. **组织类型区分**：`orgType`（0=部门，1=单位）
3. **组织架构树节点类型**：新增 `unit` 类型，ID前缀规则变更

---

## 1. 用户管理接口

### 1.1 查询用户列表
**接口地址**：`GET /system/user/list`

**请求参数**：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "userName": "用户名（可选）",
  "nickName": "用户昵称（可选）",
  "status": "状态（可选）",
  "phonenumber": "手机号（可选）",
  "deptId": "直接所属组织ID（可选）",
  "orgId": "所属单位ID（可选，新增）",
  "beginTime": "开始时间（可选）",
  "endTime": "结束时间（可选）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "phonenumber": "13800138000",
      "sex": "0",
      "status": "0",
      "deptId": 100,
      "orgId": 100,
      "createTime": "2023-01-01 00:00:00",
      "dept": {
        "deptId": 100,
        "deptName": "应急管理局",
        "orgType": "1"
      }
    }
  ],
  "total": 1
}
```

**变更说明**：
- 新增 `orgId` 请求参数，用于按单位过滤用户
- 返回数据中新增 `orgId` 字段
- dept 对象中新增 `orgType` 字段

### 1.2 获取用户详情
**接口地址**：`GET /system/user/{userId}`

**请求参数**：
- `userId`：用户ID（路径参数）

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>",
    "phonenumber": "13800138000",
    "sex": "0",
    "status": "0",
    "deptId": 100,
    "orgId": 100,
    "roleIds": [1],
    "postIds": [1],
    "dept": {
      "deptId": 100,
      "deptName": "应急管理局",
      "orgType": "1"
    }
  }
}
```

**变更说明**：
- 返回数据中新增 `orgId` 字段

### 1.3 新增用户
**接口地址**：`POST /system/user`

**请求参数**：
```json
{
  "userName": "testuser",
  "nickName": "测试用户",
  "email": "<EMAIL>",
  "phonenumber": "13800138001",
  "sex": "0",
  "status": "0",
  "deptId": 101,
  "password": "123456",
  "roleIds": [2],
  "postIds": [2]
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

**变更说明**：
- 系统会根据 `deptId` 自动计算并设置 `orgId`
- 如果 `deptId` 是单位，则 `orgId = deptId`
- 如果 `deptId` 是部门，则 `orgId` 为该部门所属的单位ID

### 1.4 修改用户
**接口地址**：`PUT /system/user`

**请求参数**：
```json
{
  "userId": 1,
  "userName": "testuser",
  "nickName": "测试用户",
  "email": "<EMAIL>",
  "phonenumber": "13800138001",
  "sex": "0",
  "status": "0",
  "deptId": 102,
  "roleIds": [2],
  "postIds": [2]
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

**变更说明**：
- 系统会根据新的 `deptId` 重新计算并更新 `orgId`

---

## 2. 部门管理接口

### 2.1 查询部门列表
**接口地址**：`GET /system/dept/list`

**请求参数**：
```json
{
  "deptName": "部门名称（可选）",
  "status": "状态（可选）",
  "orgType": "组织类型（可选，新增）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "deptId": 100,
      "parentId": 0,
      "ancestors": "0",
      "deptName": "应急管理局",
      "orderNum": 0,
      "leader": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "status": "0",
      "orgType": "1",
      "children": []
    }
  ]
}
```

**变更说明**：
- 新增 `orgType` 请求参数
- 返回数据中新增 `orgType` 字段

### 2.2 获取单位列表（新增）
**接口地址**：`GET /system/dept/units`

**请求参数**：
```json
{
  "deptName": "单位名称（可选）",
  "status": "状态（可选）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "deptId": 100,
      "parentId": 0,
      "ancestors": "0",
      "deptName": "应急管理局",
      "orderNum": 0,
      "leader": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "status": "0",
      "orgType": "1",
      "children": []
    }
  ]
}
```

**变更说明**：
- 新增接口，仅返回单位类型（orgType="1"）的组织

### 2.3 获取部门列表（新增）
**接口地址**：`GET /system/dept/departments`

**请求参数**：
```json
{
  "deptName": "部门名称（可选）",
  "status": "状态（可选）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "deptId": 101,
      "parentId": 100,
      "ancestors": "0,100",
      "deptName": "综合科",
      "orderNum": 1,
      "leader": "李四",
      "phone": "13800138001",
      "email": "<EMAIL>",
      "status": "0",
      "orgType": "0",
      "children": []
    }
  ]
}
```

**变更说明**：
- 新增接口，仅返回部门类型（orgType="0"）的组织

### 2.4 获取部门树选择
**接口地址**：`GET /system/dept/treeselect`

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 100,
      "label": "应急管理局",
      "children": [
        {
          "id": 101,
          "label": "综合科",
          "children": []
        }
      ]
    }
  ]
}
```

**变更说明**：
- 返回数据结构保持不变，但现在包含单位和部门的完整层级结构

### 2.5 获取单位-部门层级树（新增）
**接口地址**：`GET /system/dept/unitDeptTree`

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 100,
      "label": "应急管理局（单位）",
      "children": [
        {
          "id": 101,
          "label": "综合科（部门）",
          "children": []
        },
        {
          "id": 102,
          "label": "救援科（部门）",
          "children": []
        }
      ]
    }
  ]
}
```

**变更说明**：
- 新增接口，单位作为一级节点，部门作为二级节点

---

## 3. 组织架构树接口

### 3.1 获取组织架构树
**接口地址**：`GET /system/organization/tree`

**请求参数**：
```json
{
  "deptId": "根节点ID（可选）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "unit_100",
      "name": "应急管理局",
      "type": "unit",
      "deptId": 100,
      "parentId": null,
      "phone": "13800138000",
      "email": "<EMAIL>",
      "leader": "张三",
      "status": "0",
      "children": [
        {
          "id": "dept_101",
          "name": "综合科",
          "type": "dept",
          "deptId": 101,
          "parentId": "unit_100",
          "phone": "13800138001",
          "email": "<EMAIL>",
          "leader": "李四",
          "status": "0",
          "children": [
            {
              "id": "post_1",
              "name": "科长",
              "type": "post",
              "postId": 1,
              "parentId": "dept_101",
              "postCode": "001",
              "status": "0",
              "children": [
                {
                  "id": "user_1",
                  "name": "张三",
                  "type": "user",
                  "userId": 1,
                  "parentId": "post_1",
                  "phone": "13800138000",
                  "email": "<EMAIL>",
                  "userName": "zhangsan",
                  "status": "0",
                  "children": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

**变更说明**：
- `type` 字段新增 `unit` 类型
- `id` 字段前缀规则：`unit_` 表示单位，`dept_` 表示部门
- `parentId` 字段根据组织类型使用相应前缀

### 3.2 获取单位树（新增）
**接口地址**：`GET /system/organization/unitTree`

**请求参数**：
```json
{
  "deptId": "根单位ID（可选）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "unit_100",
      "name": "应急管理局",
      "type": "unit",
      "deptId": 100,
      "parentId": null,
      "children": [
        {
          "id": "post_1",
          "name": "局长",
          "type": "post",
          "postId": 1,
          "parentId": "unit_100",
          "children": []
        }
      ]
    }
  ]
}
```

**变更说明**：
- 新增接口，仅返回单位及其下的岗位和人员

### 3.3 获取部门树（新增）
**接口地址**：`GET /system/organization/deptTree`

**请求参数**：
```json
{
  "deptId": "根部门ID（可选）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "dept_101",
      "name": "综合科",
      "type": "dept",
      "deptId": 101,
      "parentId": null,
      "children": [
        {
          "id": "post_2",
          "name": "科长",
          "type": "post",
          "postId": 2,
          "parentId": "dept_101",
          "children": []
        }
      ]
    }
  ]
}
```

**变更说明**：
- 新增接口，仅返回部门及其下的岗位和人员

### 3.4 获取单位-部门层级组织树（新增）
**接口地址**：`GET /system/organization/unitDeptTree`

**请求参数**：
```json
{
  "deptId": "根节点ID（可选）"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "unit_100",
      "name": "应急管理局",
      "type": "unit",
      "deptId": 100,
      "parentId": null,
      "children": [
        {
          "id": "dept_101",
          "name": "综合科",
          "type": "dept",
          "deptId": 101,
          "parentId": "unit_100",
          "children": [
            {
              "id": "post_2",
              "name": "科长",
              "type": "post",
              "postId": 2,
              "parentId": "dept_101",
              "children": []
            }
          ]
        }
      ]
    }
  ]
}
```

**变更说明**：
- 新增接口，单位作为一级节点，部门作为二级节点，包含完整的岗位和人员信息

---

## 4. 字典接口

### 4.1 获取组织类型字典（新增）
**接口地址**：`GET /system/dict/data/type/org_type`

**返回数据**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "dictCode": 1,
      "dictSort": 1,
      "dictLabel": "部门",
      "dictValue": "0",
      "dictType": "org_type",
      "cssClass": "",
      "listClass": "primary",
      "isDefault": "Y",
      "status": "0"
    },
    {
      "dictCode": 2,
      "dictSort": 2,
      "dictLabel": "单位",
      "dictValue": "1",
      "dictType": "org_type",
      "cssClass": "",
      "listClass": "success",
      "isDefault": "N",
      "status": "0"
    }
  ]
}
```

**变更说明**：
- 新增字典类型，用于前端组织类型选择

---

## 5. 前端适配建议

### 5.1 用户管理页面
1. **用户列表**：
   - 显示用户所属单位信息（通过 `orgId` 关联）
   - 添加按单位过滤的功能
   - 在用户信息中区分显示直接所属组织和所属单位

2. **用户新增/编辑**：
   - 组织选择器需要支持单位和部门的区分显示
   - 可以使用不同图标区分单位和部门
   - 提示用户选择的是单位还是部门

### 5.2 组织架构树
1. **节点类型区分**：
   - `unit` 类型使用单位图标
   - `dept` 类型使用部门图标
   - `post` 类型使用岗位图标
   - `user` 类型使用用户图标

2. **ID处理**：
   - 注意新的ID前缀规则（`unit_`、`dept_`、`post_`、`user_`）
   - 在处理节点点击事件时需要解析ID前缀

### 5.3 数据处理
1. **兼容性**：
   - 现有代码中使用 `deptId` 的地方需要检查是否需要改为 `orgId`
   - 新增的 `orgId` 字段需要在相关表单和显示中处理

2. **验证逻辑**：
   - 可以使用用户实体的 `isDirectlyBelongToUnit()` 和 `isBelongToDepartment()` 方法进行业务判断

这些接口变更主要是为了支持单位-部门的区分功能，大部分现有接口保持向后兼容，新增的接口提供了更精细的查询和管理能力。
