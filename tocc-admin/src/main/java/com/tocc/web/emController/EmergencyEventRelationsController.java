package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tocc.domain.entity.EmergencyEventRelations;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;

import com.tocc.emergency.service.IEmergencyEventRelationsService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急事件关联Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Api(tags ="应急事件关联")
@RestController
@RequestMapping("/emergency/relations")
public class EmergencyEventRelationsController extends BaseController
{
    @Autowired
    private IEmergencyEventRelationsService emergencyEventRelationsService;

    /**
     * 查询应急事件关联列表
     */
    @ApiOperation("查询应急事件关联列表")
    @PreAuthorize("@ss.hasPermi('emergency:relations:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmergencyEventRelations emergencyEventRelations)
    {
        startPage();
        List<EmergencyEventRelations> list = emergencyEventRelationsService.selectEmergencyEventRelationsList(emergencyEventRelations);
        return getDataTable(list);
    }

    /**
     * 导出应急事件关联列表
     */
    @ApiOperation("导出应急事件关联列表")
    @PreAuthorize("@ss.hasPermi('emergency:relations:export')")
    @Log(title = "应急事件关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmergencyEventRelations emergencyEventRelations)
    {
        List<EmergencyEventRelations> list = emergencyEventRelationsService.selectEmergencyEventRelationsList(emergencyEventRelations);
        ExcelUtil<EmergencyEventRelations> util = new ExcelUtil<EmergencyEventRelations>(EmergencyEventRelations.class);
        util.exportExcel(response, list, "应急事件关联数据");
    }

    /**
     * 获取应急事件关联详细信息
     */
    @ApiOperation("获取应急事件关联详细信息")
    @PreAuthorize("@ss.hasPermi('emergency:relations:query')")
    @GetMapping(value = "/{eventRelationsId}")
    public AjaxResult getInfo(@PathVariable("eventRelationsId") String eventRelationsId)
    {
        return success(emergencyEventRelationsService.selectEmergencyEventRelationsByEventRelationsId(eventRelationsId));
    }

    /**
     * 新增应急事件关联
     */
    @ApiOperation("新增应急事件关联")
    @PreAuthorize("@ss.hasPermi('emergency:relations:add')")
    @Log(title = "应急事件关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmergencyEventRelations emergencyEventRelations)
    {
        return toAjax(emergencyEventRelationsService.insertEmergencyEventRelations(emergencyEventRelations));
    }

    /**
     * 修改应急事件关联
     */
    @ApiOperation("修改应急事件关联")
    @PreAuthorize("@ss.hasPermi('emergency:relations:edit')")
    @Log(title = "应急事件关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmergencyEventRelations emergencyEventRelations)
    {
        return toAjax(emergencyEventRelationsService.updateEmergencyEventRelations(emergencyEventRelations));
    }

    /**
     * 删除应急事件关联
     */
    @ApiOperation("删除应急事件关联")
    @PreAuthorize("@ss.hasPermi('emergency:relations:remove')")
    @Log(title = "应急事件关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{eventRelationsIds}")
    public AjaxResult remove(@PathVariable String[] eventRelationsIds)
    {
        return toAjax(emergencyEventRelationsService.deleteEmergencyEventRelationsByEventRelationsIds(eventRelationsIds));
    }
}
