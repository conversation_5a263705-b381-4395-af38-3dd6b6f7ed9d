package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tocc.risk.domain.InspectIssued;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.InspectTask;
import com.tocc.risk.service.IInspectTaskService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 检查任务Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags =  "检查任务")
@RestController
@RequestMapping("/risk/inspectTask")
public class InspectTaskController extends BaseController
{
    @Autowired
    private IInspectTaskService inspectTaskService;

    /**
     * 查询检查任务列表
     */
    @ApiOperation("获取检查任务列表")
    @PreAuthorize("@ss.hasPermi('system:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(InspectTask inspectTask)
    {
        startPage();
        List<InspectIssued> list = inspectTaskService.selectInspectTaskList(inspectTask);
        return getDataTable(list);
    }

    /**
     * 导出检查任务列表
     */
//    @PreAuthorize("@ss.hasPermi('system:task:export')")
//    @Log(title = "检查任务", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, InspectTask inspectTask)
//    {
//        List<InspectTask> list = inspectTaskService.selectInspectTaskList(inspectTask);
//        ExcelUtil<InspectTask> util = new ExcelUtil<InspectTask>(InspectTask.class);
//        util.exportExcel(response, list, "检查任务数据");
//    }

    /**
     * 获取检查任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:task:query')")
    @GetMapping(value = "/getInfo/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inspectTaskService.selectInspectTaskById(id));
    }

    /**
     * 新增检查任务
     */
//    @PreAuthorize("@ss.hasPermi('system:task:add')")
//    @Log(title = "检查任务", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody InspectTask inspectTask)
//    {
//        return toAjax(inspectTaskService.insertInspectTask(inspectTask));
//    }

    /**
     * 修改检查任务
     */
    @ApiOperation("直接完成")
//    @PreAuthorize("@ss.hasPermi('system:task:edit')")
    @Log(title = "检查任务直接完成", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody InspectTask inspectTask)
    {
        return toAjax(inspectTaskService.updateInspectTask(inspectTask));
    }

    /**
     * 删除检查任务
     */
    @PreAuthorize("@ss.hasPermi('system:task:remove')")
    @Log(title = "检查任务", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody InspectTask task)
    {
        return toAjax(inspectTaskService.deleteInspectTaskById(task.getId()));
    }

    /**
     * 查看任务详情
     */
    @Log(title = "查看任务详情", businessType = BusinessType.DELETE)
	@GetMapping("/getTaskDetails")
    public AjaxResult getTaskDetails(InspectTask task)
    {
        return success(inspectTaskService.getTaskDetails(task.getId()));
    }


}
