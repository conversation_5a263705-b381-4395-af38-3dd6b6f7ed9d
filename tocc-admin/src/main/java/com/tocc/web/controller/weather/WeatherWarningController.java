package com.tocc.web.controller.weather;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.dto.WeatherWarningDTO;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;
import com.tocc.weather.domain.vo.WeatherWarningVO;
import com.tocc.weather.service.IWeatherWarningService;

/**
 * 气象预警信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Api(tags = "气象预警管理")
@RestController
@RequestMapping("/weather/warning")
public class WeatherWarningController extends BaseController
{
    @Autowired
    private IWeatherWarningService weatherWarningService;

    /**
     * 查询气象预警信息列表
     */
    @ApiOperation("查询气象预警信息列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/list")
    public TableDataInfo list(WeatherWarningDTO weatherWarningDTO)
    {
        startPage();
        List<WeatherWarning> list = weatherWarningService.selectWeatherWarningList(weatherWarningDTO);
        return getDataTable(list);
    }

    /**
     * 导出气象预警信息列表
     */
    @ApiOperation("导出气象预警信息列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:export')")
    @Log(title = "气象预警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WeatherWarningDTO weatherWarningDTO)
    {
        List<WeatherWarning> list = weatherWarningService.selectWeatherWarningList(weatherWarningDTO);
        ExcelUtil<WeatherWarning> util = new ExcelUtil<WeatherWarning>(WeatherWarning.class);
        util.exportExcel(response, list, "气象预警信息数据");
    }

    /**
     * 获取气象预警信息详细信息
     */
    @ApiOperation("获取气象预警信息详细信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:query')")
    @GetMapping(value = "/{warningId}")
    public AjaxResult getInfo(@PathVariable("warningId") String warningId)
    {
        WeatherWarningVO vo = weatherWarningService.selectWeatherWarningDetail(warningId);
        return success(vo);
    }

    /**
     * 新增气象预警信息
     */
    @ApiOperation("新增气象预警信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:add')")
    @Log(title = "气象预警信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody WeatherWarningCreateDTO createDTO)
    {
        String warningId = weatherWarningService.createWarningAndNotify(createDTO);
        return success("预警创建成功，ID：" + warningId);
    }

    /**
     * 修改气象预警信息
     */
    @ApiOperation("修改气象预警信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:edit')")
    @Log(title = "气象预警信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WeatherWarning weatherWarning)
    {
        return toAjax(weatherWarningService.updateWeatherWarning(weatherWarning));
    }

    /**
     * 删除气象预警信息
     */
    @ApiOperation("删除气象预警信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:remove')")
    @Log(title = "气象预警信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{warningIds}")
    public AjaxResult remove(@PathVariable String[] warningIds)
    {
        return toAjax(weatherWarningService.deleteWeatherWarningByWarningIds(warningIds));
    }

    /**
     * 更新预警状态
     */
    @ApiOperation("更新预警状态")
    @PreAuthorize("@ss.hasPermi('weather:warning:edit')")
    @Log(title = "更新预警状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{warningId}/status")
    public AjaxResult updateStatus(@PathVariable String warningId, @RequestBody String status)
    {
        return toAjax(weatherWarningService.updateWeatherWarningStatus(warningId, status));
    }

    /**
     * 取消预警
     */
    @ApiOperation("取消预警")
    @PreAuthorize("@ss.hasPermi('weather:warning:edit')")
    @Log(title = "取消预警", businessType = BusinessType.UPDATE)
    @PutMapping("/{warningId}/cancel")
    public AjaxResult cancelWarning(@PathVariable String warningId, @RequestBody String reason)
    {
        return toAjax(weatherWarningService.cancelWarning(warningId, reason));
    }

    /**
     * 查询有效的预警列表
     */
    @ApiOperation("查询有效的预警列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/active")
    public AjaxResult getActiveWarnings()
    {
        List<WeatherWarning> list = weatherWarningService.selectActiveWarnings();
        return success(list);
    }

    /**
     * 查询即将过期的预警列表
     */
    @ApiOperation("查询即将过期的预警列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/expiring")
    public AjaxResult getExpiringWarnings(Integer minutes)
    {
        if (minutes == null) {
            minutes = 60; // 默认1小时
        }
        List<WeatherWarning> list = weatherWarningService.selectExpiringWarnings(minutes);
        return success(list);
    }

    /**
     * 测试接口 - 不需要权限
     */
    @ApiOperation("测试接口")
    @GetMapping("/test")
    public AjaxResult test()
    {
        return success("气象预警模块正常运行");
    }
}
