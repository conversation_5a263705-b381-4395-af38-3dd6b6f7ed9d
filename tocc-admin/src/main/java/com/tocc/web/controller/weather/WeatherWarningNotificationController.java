package com.tocc.web.controller.weather;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.weather.domain.entity.WeatherWarningNotification;
import com.tocc.weather.domain.vo.WeatherWarningProgressVO;
import com.tocc.weather.service.IWeatherWarningNotificationService;

/**
 * 气象预警通知记录Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Api(tags = "气象预警通知管理")
@RestController
@RequestMapping("/weather/notification")
public class WeatherWarningNotificationController extends BaseController
{
    @Autowired
    private IWeatherWarningNotificationService weatherWarningNotificationService;

    /**
     * 查询预警通知进展
     */
    @ApiOperation("查询预警通知进展")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/progress/{warningId}")
    public AjaxResult getNotificationProgress(@PathVariable("warningId") String warningId)
    {
        List<WeatherWarningProgressVO> list = weatherWarningNotificationService.selectNotificationProgress(warningId);
        return success(list);
    }

    /**
     * 确认预警通知
     */
    @ApiOperation("确认预警通知")
    @PreAuthorize("@ss.hasPermi('weather:notification:confirm')")
    @Log(title = "确认预警通知", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{warningId}/{contactUserId}")
    public AjaxResult confirmNotification(@PathVariable("warningId") String warningId, 
                                        @PathVariable("contactUserId") Long contactUserId)
    {
        Long confirmUserId = SecurityUtils.getUserId();
        String confirmUserName = SecurityUtils.getUsername();
        
        int result = weatherWarningNotificationService.confirmNotification(warningId, contactUserId, confirmUserId, confirmUserName);
        
        if (result > 0) {
            return success("确认成功");
        } else {
            return error("确认失败，请检查通知是否存在或已确认");
        }
    }

    /**
     * 催办未确认通知
     */
    @ApiOperation("催办未确认通知")
    @PreAuthorize("@ss.hasPermi('weather:warning:edit')")
    @Log(title = "催办预警通知", businessType = BusinessType.OTHER)
    @PostMapping("/remind/{warningId}")
    public AjaxResult remindNotifications(@PathVariable("warningId") String warningId)
    {
        int count = weatherWarningNotificationService.remindUnconfirmedNotifications(warningId);
        return success("催办通知已发送，共" + count + "条");
    }

    /**
     * 查询当前用户的预警通知列表
     */
    @ApiOperation("查询当前用户的预警通知列表")
    @PreAuthorize("@ss.hasPermi('weather:notification:list')")
    @GetMapping("/my")
    public AjaxResult getMyNotifications()
    {
        Long userId = SecurityUtils.getUserId();
        List<WeatherWarningNotification> list = weatherWarningNotificationService.selectNotificationsByUserId(userId);
        return success(list);
    }

    /**
     * 查询指定预警的通知列表
     */
    @ApiOperation("查询指定预警的通知列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/list/{warningId}")
    public AjaxResult getNotificationList(@PathVariable("warningId") String warningId)
    {
        List<WeatherWarningNotification> list = weatherWarningNotificationService.selectWeatherWarningNotificationList(warningId);
        return success(list);
    }

    /**
     * 查询未确认的通知列表
     */
    @ApiOperation("查询未确认的通知列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/unconfirmed/{warningId}")
    public AjaxResult getUnconfirmedNotifications(@PathVariable("warningId") String warningId)
    {
        List<WeatherWarningNotification> list = weatherWarningNotificationService.selectUnconfirmedNotifications(warningId);
        return success(list);
    }

    /**
     * 查询超时的通知列表
     */
    @ApiOperation("查询超时的通知列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/timeout")
    public AjaxResult getTimeoutNotifications()
    {
        List<WeatherWarningNotification> list = weatherWarningNotificationService.selectTimeoutNotifications();
        return success(list);
    }
}
